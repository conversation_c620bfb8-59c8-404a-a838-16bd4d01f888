import React from 'react';
import {
  Table,
  TextInput,
  ScrollArea,
  Loader,
  Center,
  Pagination,
} from '@mantine/core';

type Column = {
  key: string;
  label: string;
  isShown: boolean;
  isSearchable?: boolean;
  isRequired?: boolean;
};
interface DataTableProps<T> {
  columns: Column[];
  data: T[];
  loading: boolean;
  onSearchChange: (columnKey: string, value: string) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function DataTable<T extends Record<string, string | number | boolean | null | undefined>>({
  columns,
  data,
  loading,
  onSearchChange,
  currentPage,
  totalPages,
  onPageChange,
}: DataTableProps<T>) {
  return (
    <>
      <ScrollArea>
        <Table striped withTableBorder withColumnBorders>
          <thead>
            <tr>
              {columns
                .filter((col) => col.isShown)
                .map((col) => (
                  <th key={col.key}>{col.label}</th>
                ))}
            </tr>
            <tr>
              {columns
                .filter((col) => col.isShown)
                .map((col) =>
                  col.isSearchable ? (
                    <th key={col.key}>
                      <TextInput
                        placeholder="Rechercher"
                        onChange={(e) =>
                          onSearchChange(col.key, e.currentTarget.value)
                        }
                      />
                    </th>
                  ) : (
                    <th key={col.key}></th>
                  )
                )}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={columns.length}>
                  <Center py="md">
                    <Loader />
                  </Center>
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={columns.length}>
                  <Center py="md">Aucun élément trouvé</Center>
                </td>
              </tr>
            ) : (
              data.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {columns
                    .filter((col) => col.isShown)
                    .map((col) => (
                      <td key={col.key}>{row[col.key] ?? ''}</td>
                    ))}
                </tr>
              ))
            )}
          </tbody>
        </Table>
      </ScrollArea>

      <Center mt="md">
        <Pagination
          total={totalPages}
          value={currentPage}
          onChange={onPageChange}
        />
      </Center>
    </>
  );
}
