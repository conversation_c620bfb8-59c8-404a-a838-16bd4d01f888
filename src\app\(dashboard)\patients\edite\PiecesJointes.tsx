


import { Group, Title, Button, Text, Tooltip, Menu,ActionIcon } from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import AttachmentManager from './AttachmentManager'
import Icon from '@mdi/react';
import {
  mdiArrowLeft,
  mdiCardAccountDetails,
  mdiApps,
  mdiAccountAlert,
  mdiAccountSupervisorCircle,
  mdiCalendarText,
  mdiCashMultiple,
  mdiCurrencyUsd,
  mdiTooth,
  mdiCertificate,
  mdiFormatListBulleted,
   mdiBarcode,
  mdiSkipPrevious,
  mdiSkipNext,
  mdiCalendarPlus,
} from '@mdi/js';
import { Patient, PatientActionsProps } from './types';

export interface PiecesJointesPatientActionsProps extends PatientActionsProps {
  patientId?: string;
  isFormInvalid: boolean;
  isDraft: boolean;
  onPrint?: () => void;
  onPrevious?: () => void;
  onNext?: () => void;
  onStartVisit?: () => void;
  onAppointment?: () => void;
  onCancel?: () => void;
  onSaveQuitNew?: () => void;
  onSaveQuit?: () => void;
  onSubmit?: () => void;
   patient: Patient;
  onGoBack: () => void;
  onAddMeasurement: () => void;
  onGoToContract: () => void;
};
export const PiecesJointes =({
  patient,
  onGoBack,
  onGoToContract,
   patientId,
  isFormInvalid,
  isDraft,
  onPrint,
  onPrevious,
  onNext,
  onStartVisit,
  onAppointment,
  onCancel,
  onSaveQuitNew,
  onSaveQuit,
  onSubmit,
}: PiecesJointesPatientActionsProps) => {
     const disabled = isFormInvalid || isDraft;
  return (
    <>
    <div className="bg-[#3799ce] text-white px-4 py-3 rounded-t-lg">
       <Group justify="space-between" align="center">
         <Group>
           {patient ? (
             <Icon path={mdiCardAccountDetails} size={1} />
           ) : (
             <Button variant="subtle" onClick={onGoBack}>
               <Icon path={mdiArrowLeft} size={1} color={"white"}/>
             </Button>
           )}
           <Title order={2}>Fiche patient</Title>
           <DatePickerInput placeholder="Date de création" />
           23/06/2025
         </Group>
   
         {patient && (
           <Group>
             <Text>{patient.full_name}</Text>
             <Text>{patient.gender}</Text>
             <Text>{patient.age}</Text>
             <Text>{patient.default_insurance}</Text>
             <Text>{patient.file_number}</Text>
             <Text>{patient.last_visit}</Text>
           </Group>
         )}
   
         <Group>
          
         
           <Menu shadow="md" width={220}>
             <Menu.Target>
               <Button variant="subtle">
                 <Icon path={mdiApps} size={1} color={"white"}/>
               </Button>
             </Menu.Target>
             <Menu.Dropdown>
               <Menu.Item leftSection={<Icon path={mdiAccountAlert} size={0.8} />}>Alerts</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiAccountSupervisorCircle} size={0.8} />}>Relations Patient</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCalendarText} size={0.8} />}>Planifications</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCashMultiple} size={0.8} />}>État financier</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCurrencyUsd} size={0.8} />}>Nouvel encaissement</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiTooth} size={0.8} />}>Schéma dentaire</Menu.Item>
             </Menu.Dropdown>
           </Menu>
   
           <Tooltip label="Contrat">
             <Button variant="subtle" onClick={onGoToContract}>
               <Icon path={mdiCertificate} size={1} color={"white"}/>
             </Button>
           </Tooltip>
   
           <Tooltip label="Liste patients">
             <Button component="a" href="/pratisoft/patient" variant="subtle">
               <Icon path={mdiFormatListBulleted} size={1} color={"white"}/>
             </Button>
           </Tooltip>
         </Group>
       </Group>
       
       </div>
{/* --------------------------Start Content ------------------------------*/}
<AttachmentManager patientId="12345" />
{/* -------------------------end  Content---------------------------------*/}
    
     <div style={{marginTop:"120px" , borderTop: "1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-5))"}}>
      
       <Group justify="space-between" wrap="wrap" mt="md" mb={"auto"}>
    <Group gap="xs">
        {patientId && (
        <>
            <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onPrint}>
            <Icon path={mdiBarcode} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onPrevious}>
            <Icon path={mdiSkipPrevious} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
<Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onNext}>
            <Icon path={mdiSkipNext} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
        </>
        )}
    </Group>

    <Group gap="xs">
        <Tooltip label="Commencer la visite">
        <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onStartVisit}
            disabled={disabled}>
        <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Tooltip label="Ajouter un rendez-vous">
        <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onAppointment}
            disabled={isFormInvalid}>
            <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Button variant="outline" color="red" onClick={onCancel}>
        Annuler
        </Button>

        {patientId && (
        <Button
            variant="filled"
            color="blue"
            onClick={onSaveQuitNew}
            disabled={isFormInvalid}
        >
            Enregistrer & Nouvelle fiche
        </Button>
        )}

        <Button
        variant="filled"
        color="blue"
        onClick={onSaveQuit}
        disabled={isFormInvalid}
        >
        Enregistrer et quitter
        </Button>

        <Button
        variant="filled"
        color="blue"
        type="submit"
        onClick={onSubmit}
        disabled={isFormInvalid}
        >
        Enregistrer la fiche
        </Button>
    </Group>
    </Group>
      
    </div>
    </>
  )
}






