
'use client';
import React, { useState } from 'react';
import { Group, Title, Button, Text, Tooltip, ActionIcon, Table, ScrollArea, Card, Autocomplete } from '@mantine/core';

import { MeasurementDialog } from './MeasurementDialog';
import { MeasurementTrendsDialog } from './MeasurementTrendsDialog';
import Icon from '@mdi/react';
import {
  mdiArrowLeft,
  mdiCardAccountDetails,
  mdiPlus,
  mdiTooth,
  
  mdiBarcode,
  mdiSkipPrevious,
  mdiSkipNext,
  mdiCalendarPlus,
  mdiChartLine,
  mdiDeleteSweep,
  mdiPencil,
  mdiMinus,
  mdiPlusBox
} from '@mdi/js';

const items = [
  {
    id: '1',
    name: 'Poids',
    data: [60, 62, 63.5, 61.8, 65],
    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai'],
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    data: [170, 170, 170, 170, 170],
    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai'],
  },
];

interface Measurement {
  id: string;
  date: string;
  values: { [label: string]: number | string | boolean | null };
  comment?: string;
}

interface MeasureDefinition {
  id: string;
  label: string;
  type: 'float' | 'integer' | 'boolean' | 'string' | 'date' | 'calculated';
}
type PatientActionsProps = {
  patientId?: string;
  isFormInvalid: boolean;
  isDraft: boolean;
  onPrint?: () => void;
  onPrevious?: () => void;
  onNext?: () => void;
  onStartVisit?: () => void;
  onAppointment?: () => void;
  onCancel?: () => void;
  onSaveQuitNew?: () => void;
  onSaveQuit?: () => void;
  onSubmit?: () => void;
   patient: any;
  onGoBack: () => void;
  onAddMeasurement: () => void;
  onGoToContract: () => void;
   measures: MeasureDefinition[];
  measurements: Measurement[];
};
export const Biometrie = ({
  patient,
  onGoBack,
 
  patientId,
  isFormInvalid,
  isDraft,
  onPrint,
  onPrevious,
  onNext,
  onStartVisit,
  onAppointment,
  onCancel,
  onSaveQuitNew,
  onSaveQuit,
  onSubmit,
  measures,
  measurements
}: PatientActionsProps) => {
  const disabled = isFormInvalid || isDraft;

  const [modalOpen, setModalOpen] = useState(false);
  const [measurementmodalOpen, setMeasurementModalOpen] = useState(false);

  // États pour les chips de biométrie avec correction du bouton delete
  const [biometrieChips, setBiometrieChips] = useState<Array<{id?: string, label: string}>>([
    { id: '1', label: 'Poids: 70 kg' },
    { id: '2', label: 'Taille: 175 cm' },
    { id: '3', label: 'IMC: 22.9' }
  ]);

  const [searchText, setSearchText] = useState('');
  const [readOnly, ] = useState(false);
  const [autocompleteData] = useState([
    'Poids: 65 kg',
    'Poids: 70 kg',
    'Poids: 75 kg',
    'Taille: 160 cm',
    'Taille: 170 cm',
    'Taille: 175 cm',
    'Taille: 180 cm',
    'IMC: 18.5',
    'IMC: 22.9',
    'IMC: 25.0',
    'Tension artérielle: 120/80 mmHg',
    'Fréquence cardiaque: 72 bpm',
    'Température: 36.5°C',
    'Saturation O2: 98%'
  ]);

  // Fonctions pour gérer les chips - CORRIGÉES
  const addBiometrieChip = (item: string) => {
    if (item.trim() && !biometrieChips.find(chip => chip.label === item)) {
      setBiometrieChips(prev => [...prev, { label: item }]);
      setSearchText('');
    }
  };

  const removeBiometrieChip = (index: number) => {
    setBiometrieChips(prev => prev.filter((_, i) => i !== index));
  };

  const editBiometrieChip = (chip: {id?: string, label: string}, event: React.MouseEvent) => {
    event.stopPropagation();
    console.log('Éditer chip biométrie:', chip);
  };

  // Fonction pour vider tous les chips - CORRIGÉE
  const clearAllBiometrieChips = () => {
    setBiometrieChips([]);
    setSearchText('');
  };

  // Données par défaut si measures/measurements ne sont pas définis
  const defaultMeasures: MeasureDefinition[] = [
    { id: '1', label: 'Poids (Kg)', type: 'float' },
    { id: '2', label: 'Taille (cm)', type: 'integer' },
    { id: '3', label: 'IMC (Kg/m²)', type: 'calculated' },
    { id: '4', label: 'Pouls (/min)', type: 'integer' },
    { id: '5', label: 'T° (C°)', type: 'float' },
    { id: '6', label: 'T.A SYS (mmHg)', type: 'integer' },
    { id: '7', label: 'T.A DIA (mmHg)', type: 'integer' },
    { id: '8', label: 'SO2 (%)', type: 'integer' }
  ];

  const defaultMeasurements: Measurement[] = [
    {
      id: '1',
      date: '28/06/2025',
      values: {
        'Poids (Kg)': 70,
        'Taille (cm)': 175,
        'IMC (Kg/m²)': 22.9,
        'Pouls (/min)': 72,
        'T° (C°)': 36.5,
        'T.A SYS (mmHg)': 120,
        'T.A DIA (mmHg)': 80,
        'SO2 (%)': 98
      },
      comment: 'Mesures normales'
    }
  ];

  const safeMeasures = measures || defaultMeasures;
  const safeMeasurements = measurements || defaultMeasurements;

  const headers = [
    <Table.Th key="date">Date</Table.Th>,
    ...safeMeasures.map((m: MeasureDefinition) => (
      <Table.Th key={m.id}>{m.label}</Table.Th>
    )),
    <Table.Th key="comment">Commentaire</Table.Th>,
  ];

  const rows =
    safeMeasurements.length === 0 ? (
      <Table.Tr>
        <Table.Td colSpan={safeMeasures.length + 2}>
          <Text c="dimmed" ta="center">
            Aucune biométrie à afficher
          </Text>
        </Table.Td>
      </Table.Tr>
    ) : (
      safeMeasurements.map((m: Measurement) => (
        <Table.Tr key={m.id}>
          <Table.Td>{m.date}</Table.Td>
          {safeMeasures.map((def: MeasureDefinition) => (
            <Table.Td key={def.id}>
              {m.values[def.label] != null ? String(m.values[def.label]) : ''}
            </Table.Td>
          ))}
          <Table.Td>{m.comment || ''}</Table.Td>
        </Table.Tr>
      ))
    );
  return (
    <>
      {/* En-tête moderne */}
      <div style={{
        backgroundColor: '#3799ce',
        color: 'white',
        padding: '16px',
        marginBottom: '20px',
        borderRadius: '8px'
      }}>
        <Group justify="space-between" align="center">
          <Group align="center">
            <ActionIcon variant="transparent" onClick={onGoBack}>
              <Icon path={mdiArrowLeft} size={1} color="white" />
            </ActionIcon>
            <Icon path={mdiCardAccountDetails} size={1} color="white" />
            <Title order={3} c="white">Fiche patient - Biométrie</Title>
          </Group>

          <Group>
            <Button
              leftSection={<Icon path={mdiPlus} size={0.8} />}
              variant="white"
              onClick={() => setModalOpen(true)}
            >
              Ajouter des biométries
            </Button>
            <Button
              leftSection={<Icon path={mdiChartLine} size={0.8} />}
              variant="outline"
              c="white"
              onClick={() => setMeasurementModalOpen(true)}
            >
              Tendances
            </Button>
          </Group>
        </Group>

        {patient && (
          <Group mt="md" c="white">
            <Text>{patient.full_name}</Text>
            <Text>{patient.gender}</Text>
            <Text>{patient.age}</Text>
            <Text>{patient.default_insurance}</Text>
            <Text>{patient.file_number}</Text>
          </Group>
        )}
      </div>

      {/* Composant Biométrie avec chips et bouton delete corrigé */}
      <div style={{ marginTop: '16px', marginBottom: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
          <Text fw={500} style={{ flex: 1 }}>Mesures biométriques</Text>
          <Group gap="xs">
            <ActionIcon
              variant="transparent"
              onClick={() => setModalOpen(true)}
              title="Ajouter une mesure"
            >
              <Icon path={mdiPlusBox} size={1} color="#3799ce" />
            </ActionIcon>
            <ActionIcon
              variant="transparent"
              onClick={clearAllBiometrieChips}
              disabled={biometrieChips.length === 0}
              title="Vider le champ"
            >
              <Icon path={mdiDeleteSweep} size={1} color="#e53935" />
            </ActionIcon>
          </Group>
        </div>

        {/* Chips existants */}
        <Card shadow="0" padding="lg" radius="md" withBorder>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginBottom: '12px' }}>
            {biometrieChips.map((chip, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  backgroundColor: '#f3e5f5',
                  borderRadius: '16px',
                  padding: '4px 12px',
                  fontSize: '14px',
                  border: '1px solid #ce93d8'
                }}
              >
                {!readOnly && (
                  <ActionIcon
                    variant="transparent"
                    size="xs"
                    onClick={(e) => editBiometrieChip(chip, e)}
                    style={{ marginRight: '4px' }}
                  >
                    <Icon path={mdiPencil} size={0.6} color="#7b1fa2" />
                  </ActionIcon>
                )}
                <span title={chip.label}>{chip.label}</span>
                {!readOnly && (
                  <ActionIcon
                    variant="transparent"
                    size="xs"
                    onClick={() => removeBiometrieChip(index)}
                    style={{ marginLeft: '4px' }}
                  >
                    <Icon path={mdiMinus} size={0.6} color="#d32f2f" />
                  </ActionIcon>
                )}
              </div>
            ))}
          </div>

          {/* Input avec Autocomplete */}
          {!readOnly && (
            <Autocomplete
              placeholder="Saisir une mesure biométrique"
              data={autocompleteData}
              value={searchText}
              onChange={setSearchText}
              onOptionSubmit={(item: string) => addBiometrieChip(item)}
              maxDropdownHeight={200}
              limit={10}
              clearable
            />
          )}
        </Card>
      </div>
      {/* Tableau des mesures moderne */}
      <ScrollArea>
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                {headers}
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {rows}
            </Table.Tbody>
          </Table>
        </Card>
      </ScrollArea>
    
     <div style={{marginTop:"120px" , borderTop: "1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-5))"}}>
      
       <Group justify="space-between" wrap="wrap" mt="md" mb={"auto"}>
    <Group gap="xs">
        {patientId && (
        <>
            <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onPrint}>
            <Icon path={mdiBarcode} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onPrevious}>
            <Icon path={mdiSkipPrevious} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
<Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onNext}>
            <Icon path={mdiSkipNext} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
        </>
        )}
    </Group>

    <Group gap="xs">
        <Tooltip label="Commencer la visite">
        <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onStartVisit}
            disabled={disabled}>
        <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Tooltip label="Ajouter un rendez-vous">
        <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onAppointment}
            disabled={isFormInvalid}>
            <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Button variant="outline" color="red" onClick={onCancel}>
        Annuler
        </Button>

        {patientId && (
        <Button
            variant="filled"
            color="blue"
            onClick={onSaveQuitNew}
            disabled={isFormInvalid}
        >
            Enregistrer & Nouvelle fiche
        </Button>
        )}

        <Button
        variant="filled"
        color="blue"
        onClick={onSaveQuit}
        disabled={isFormInvalid}
        >
        Enregistrer et quitter
        </Button>

        <Button
        variant="filled"
        color="blue"
        type="submit"
        onClick={onSubmit}
        disabled={isFormInvalid}
        >
        Enregistrer la fiche
        </Button>
    </Group>
    </Group>
      
    </div>
    <MeasurementDialog
  opened={modalOpen}
  onClose={() => setModalOpen(false)}
  onSubmit={(values) => {
    console.log('Biométrie soumise:', values);
    setModalOpen(false);
  }}
/>
<MeasurementTrendsDialog
  opened={measurementmodalOpen}
  onClose={() => setMeasurementModalOpen(false)}
  items={items}
/>
    </>
  )
}


