// components/CustomTable.tsx
import {
  Table, TextInput, Button, Menu, Checkbox, Pagination, Tooltip, ScrollArea,
  Group, Box, Flex
} from '@mantine/core';
import { IconSearch, IconDotsVertical, IconReload, IconFileSpreadsheet } from '@tabler/icons-react';
import { useState } from 'react';

type Column = {
  key: string;
  label: string;
  isShown: boolean;
  isSearchable?: boolean;
  isOrderable?: boolean;
};

const initialColumns: Column[] = [
  { key: 'lastName', label: 'Nom', isShown: true, isSearchable: true, isOrderable: true },
  { key: 'firstName', label: 'Prénom', isShown: true, isSearchable: true, isOrderable: true },
  { key: 'acquisitionDate', label: "Date d'acquisition", isShown: true, isSearchable: true, isOrderable: true },
  { key: 'driverTitle', label: 'Titre du Driver', isShown: true, isSearchable: true, isOrderable: true },
];

const CustomTable = () => {
  const [columns, setColumns] = useState<Column[]>(initialColumns);
  const [searchValues, setSearchValues] = useState<Record<string, string>>({});
  const [query, setQuery] = useState('');
  const [page, setPage] = useState(1);

  const toggleColumnVisibility = (key: string) => {
    setColumns(prev =>
      prev.map(col => col.key === key ? { ...col, isShown: !col.isShown } : col)
    );
  };

  const handleExportExcel = () => {
    console.log('TODO: Export to Excel');
  };

  const filteredColumns = columns.filter(c => c.isShown);

  return (
    <Box>
      <Flex justify="space-between" align="center" mb="md">
        <Group>
          <Button variant="light" leftSection={<IconReload size={16} />}>Recharger</Button>
          <Button variant="light" leftSection={<IconFileSpreadsheet size={16} />} onClick={handleExportExcel}>
            Exporter
          </Button>
        </Group>
        <TextInput
          leftSection={<IconSearch size={16} />}
          placeholder="Rechercher globalement"
          value={query}
          onChange={(e) => setQuery(e.currentTarget.value)}
        />
        <Menu shadow="md" width={220}>
          <Menu.Target>
            <Button variant="light"><IconDotsVertical size={18} /></Button>
          </Menu.Target>
          <Menu.Dropdown>
            {columns.map(col => (
              <Menu.Item key={col.key} rightSection={
                <Checkbox checked={col.isShown} readOnly />
              } onClick={() => toggleColumnVisibility(col.key)}>
                {col.label}
              </Menu.Item>
            ))}
          </Menu.Dropdown>
        </Menu>
      </Flex>

      <ScrollArea>
        <Table striped withRowBorders withColumnBorders>
          <Table.Thead>
            <Table.Tr>
              {filteredColumns.map(col => (
                <Table.Th key={col.key}>{col.label}</Table.Th>
              ))}
            </Table.Tr>
            <Table.Tr>
              {filteredColumns.map(col => (
                <Table.Th key={col.key}>
                  {col.isSearchable && (
                    <TextInput
                      placeholder="Rechercher"
                      value={searchValues[col.key] || ''}
                      onChange={(e) =>
                        setSearchValues({
                          ...searchValues,
                          [col.key]: e.currentTarget.value,
                        })
                      }
                    />
                  )}
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            <Table.Tr>
              <Table.Td colSpan={filteredColumns.length}>
                Aucun élément trouvé.
              </Table.Td>
            </Table.Tr>
          </Table.Tbody>
        </Table>
      </ScrollArea>

      <Pagination total={1} page={page} onChange={setPage} mt="md" />
    </Box>
  );
};

export default CustomTable;
