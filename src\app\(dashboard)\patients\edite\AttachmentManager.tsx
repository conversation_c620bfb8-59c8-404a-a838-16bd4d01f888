import {
  ActionIcon,
  Group,
  ScrollArea,
  Stack,
  Text,
  Tooltip,
  FileInput,
  Divider,
  Loader,
  Card,
  Modal,
  Button,
} from '@mantine/core';
import {
  IconFolder,
  IconCloudUpload,
  IconMicrophone,
  IconCamera,
  IconCameraPlus,
  IconFolderPlus,
  IconFileArrowRight,
  IconDownload,
  IconFilter,
  IconFilterOff,
  IconFile,
  IconTrash,
  IconNetwork,
} from '@tabler/icons-react';
import Icon from '@mdi/react';
import { mdiFileTree, mdiCamera, mdiCameraPlus, mdiFolderPlus,mdiDownloadNetwork,mdiFileReplace,mdiLan,mdiLanConnect ,mdiDownloadOutline,mdiSortCalendarAscending,mdiViewGrid} from '@mdi/js';

import { useState } from 'react';

export default function AttachmentManager({ patientId }: AttachmentManagerProps) {
  const [files, setFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(false);
  const [ignoreContext, setIgnoreContext] = useState(false);
  const [viewGrid, setViewGrid] = useState(true);
  const [sortedByDate, setSortedByDate] = useState(true);

  const handleUpload = (uploaded: File[]) => {
    setLoading(true);
    // TODO: upload files for patient ID: ${patientId}
    console.log(`Uploading files for patient: ${patientId}`, uploaded);
    setFiles(prev => [...prev, ...uploaded]);
    setTimeout(() => setLoading(false), 1500);
  };
  // États pour tous les modals
  const [isMicrophoneVisible, setIsMicrophoneVisible] = useState(false);
  const [isCameraVisible, setIsCameraVisible] = useState(false);
  const [isCameraPlusVisible, setIsCameraPlusVisible] = useState(false);
  const [isFolderPlusVisible, setIsFolderPlusVisible] = useState(false);
  const [isFileTreeVisible, setIsFileTreeVisible] = useState(false);
  const [isNetworkVisible, setIsNetworkVisible] = useState(false);

  // Fonctions de toggle pour chaque modal
  const toggleMicrophone = () => {
    const newState = !isMicrophoneVisible;
    setIsMicrophoneVisible(newState);
    console.log('Microphone modal toggled:', newState);
  };

  const toggleCamera = () => {
    setIsCameraVisible(!isCameraVisible);
    console.log('Camera modal toggled:', !isCameraVisible);
  };

  const toggleCameraPlus = () => {
    setIsCameraPlusVisible(!isCameraPlusVisible);
    console.log('Camera Plus modal toggled:', !isCameraPlusVisible);
  };

  const toggleFolderPlus = () => {
    setIsFolderPlusVisible(!isFolderPlusVisible);
    console.log('Folder Plus modal toggled:', !isFolderPlusVisible);
  };

  const toggleFileTree = () => {
    setIsFileTreeVisible(!isFileTreeVisible);
    console.log('File Tree modal toggled:', !isFileTreeVisible);
  };

  const toggleNetwork = () => {
    setIsNetworkVisible(!isNetworkVisible);
    console.log('Network modal toggled:', !isNetworkVisible);
  };

  return (
    <Stack>
      <Group justify="space-between">
        <Group>
          <IconFolder size={24} />
          <Text fw={600}>Pièces jointes</Text>
        </Group>
        <Group gap="xs">
          <Tooltip label="Uploader fichiers">
            <ActionIcon
              variant="light"
              onClick={() => document.getElementById('fileInput')?.click()}
            >
              <IconCloudUpload size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Enregistrer audio">
            <ActionIcon
              variant="light"
              color={isMicrophoneVisible ? "blue" : "gray"}
              onClick={(event) => {
                event.preventDefault();
                console.log('Microphone button clicked, current state:', isMicrophoneVisible);
                toggleMicrophone();
              }}
            >
              <IconMicrophone size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Prendre photo (compte)">
            <ActionIcon
              variant="light"
              color={isCameraPlusVisible ? "blue" : "gray"}
              onClick={toggleCameraPlus}
            >
              <IconCameraPlus size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Prendre photo">
            <ActionIcon
              variant="light"
              color={isCameraVisible ? "blue" : "gray"}
              onClick={toggleCamera}
            >
              <IconCamera size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Nouveau dossier">
            <ActionIcon
              variant="light"
              color={isFolderPlusVisible ? "blue" : "gray"}
              onClick={toggleFolderPlus}
            >
              <IconFolderPlus size={20} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Importer (interface)">
            <ActionIcon
              variant="light"
              color={isFileTreeVisible ? "blue" : "gray"}
              onClick={toggleFileTree}
            >
              <Icon path={mdiFileTree} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Importer DCM">
            <ActionIcon
              variant="light"
              color={isNetworkVisible ? "blue" : "gray"}
              onClick={toggleNetwork}
            >
              <Icon path={mdiLan} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="FirePACS study">
            <ActionIcon variant="light" disabled>
              <Icon path={mdiDownloadNetwork} size={1} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Importer dernière interface">
            <ActionIcon variant="light">
              <Icon path={mdiFileReplace} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Dernier DCM">
            <ActionIcon variant="light">
              <Icon path={mdiLanConnect} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Dernier FirePACS">
            <ActionIcon variant="light" disabled>
             <Icon path={mdiDownloadOutline} size={1} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Filtre contexte">
            <ActionIcon
              variant={ignoreContext ? 'default' : 'filled'}
              onClick={() => setIgnoreContext(!ignoreContext)}
            >
              {ignoreContext ? <IconFilterOff size={20} /> : <IconFilter size={20} />}
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Trier par date">
            <ActionIcon variant="light" onClick={() => setSortedByDate(!sortedByDate)}>
             <Icon path={mdiSortCalendarAscending} size={1} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Changer affichage">
            <ActionIcon variant="light" onClick={() => setViewGrid(!viewGrid)}>
              <Icon path={mdiViewGrid} size={1} />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Group>

      {/* File uploader (hidden) */}
      <FileInput
        id="fileInput"
        multiple
        onChange={handleUpload}
        accept="*"
        style={{ display: 'none' }}
      />

      {/* Content */}
      {loading ? (
        <Loader />
      ) : files.length === 0 ? (
        <Text>Aucun fichier trouvé</Text>
      ) : (
        <ScrollArea>
          {/* Liste des fichiers */}
          <Stack gap="sm">
            {files.map((file, index) => (
              <Card key={index} withBorder padding="md">
                <Group justify="space-between">
                  <Group>
                    <IconFile size={24} />
                    <div>
                      <Text fw={500}>{file.name}</Text>
                      <Text size="sm" c="dimmed">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </Text>
                    </div>
                  </Group>
                  <Group gap="xs">
                    <ActionIcon variant="light" color="blue">
                      <IconDownload size={16} />
                    </ActionIcon>
                    <ActionIcon variant="light" color="red">
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Group>
              </Card>
            ))}
          </Stack>

        </ScrollArea>
      )}

      {/* Modal d'enregistrement audio moderne - DÉPLACÉ EN DEHORS */}
      <Modal
        opened={isMicrophoneVisible}
        onClose={() => {
          console.log('Modal closing...');
          setIsMicrophoneVisible(false);
        }}
        title={
          <Group>
            <IconMicrophone size={24} />
            <Text fw={500}>Enregistrer un fichier audio</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            Cliquez sur le bouton ci-dessous pour commencer l&apos;enregistrement audio.
          </Text>

          <div style={{
            padding: '20px',
            border: '2px dashed #ced4da',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <IconMicrophone size={48} color="#868e96" />
            <Text mt="sm" c="dimmed">
              Fonctionnalité d&apos;enregistrement audio en développement
            </Text>
          </div>

          <Group justify="flex-end" mt="md">
            <Button
              variant="outline"
              onClick={() => {
                console.log('Cancel button clicked');
                setIsMicrophoneVisible(false);
              }}
            >
              Annuler
            </Button>
            <Button
              leftSection={<IconMicrophone size={16} />}
              onClick={() => {
                console.log('Record button clicked');
                // Ici on ajouterait la logique d'enregistrement
                setIsMicrophoneVisible(false);
              }}
            >
              Commencer l&apos;enregistrement
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal Camera Plus */}
      <Modal
        opened={isCameraPlusVisible}
        onClose={() => setIsCameraPlusVisible(false)}
        title={
          <Group>
            <Icon path={mdiCameraPlus} size={1} />
            <Text fw={500}>Prendre photo (compte)</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            Fonctionnalité de prise de photo avec comptage automatique.
          </Text>
          <md-dialog-content class="layout-row layout-fill" cg-busy="vm.promise" id="dialogContent_263" style="position: relative;">
        <div class="md-dialog-content flex layout-row layout-align-center-center">

            <!-- ngIf: vm.error --><div class="error-content flex-nogrow layout-row layout-align-start-center ng-scope" ng-if="vm.error" style="">
                <h3 translate-once="webcam_capture_picture_cant_access_device">Impossible d’accéder au périphérique</h3>
            </div><!-- end ngIf: vm.error -->

            <div class="flex layout-row layout-align-center-center flux-controller ng-hide" ng-class="{'has-previews': vm.previews.length > 0, 'slide-left': vm.showPreview}" ng-show="vm.success" aria-hidden="true" style="">
                <video muted="" autoplay=""></video>
            </div>

            <div class="flex layout-row layout-align-center-center flux-controller preview ng-hide" md-swipe-left="vm.swipeLeft()" md-swipe-right="vm.swipeRight()" ng-class="{'has-previews': vm.previews.length > 0, 'slide-right': vm.showPreview}" ng-show="vm.success" aria-hidden="true" style="">
                <img>
            </div>

            <div class="action-container">
                <!-- ngIf: vm.showPreview -->
                <!-- ngIf: !vm.showPreview && vm.success -->
                <!-- ngIf: !vm.multi && vm.showPreview && !vm.cropper -->
                <!-- ngIf: vm.videoDevices && vm.videoDevices.length > 1 && !vm.showPreview -->
                <button class="md-fab mn-success md-button md-ink-ripple" type="button" ng-transclude="" ng-disabled="vm.items.length == 0 &amp;&amp; !vm.item" ng-click="vm.savePicture()" aria-label="switch cam" disabled="disabled">
                    <md-icon md-font-icon="mdi-check" md-font-set="mdi" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="true"></md-icon>
                </button>
                <button class="md-fab md-warn md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.cancel()" aria-label="switch cam">
                    <md-icon md-font-icon="mdi-close" md-font-set="mdi" class="ng-scope md-font mdi mdi-close" role="img" aria-hidden="true"></md-icon>
                </button>
            </div>
        </div>

        <div class="web-cam-thumbnail" ng-class="{opened: vm.previews.length > 0}">
            <!-- ngRepeat: item in vm.previews track by $index -->
        </div>
    <div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""></div><div class="cg-busy ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""><div class="cg-loader-spinner" style="position: absolute; inset: 0px;">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div></div></md-dialog-content>
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setIsCameraPlusVisible(false)}>
              Annuler
            </Button>
            <Button leftSection={<IconCameraPlus size={16} />}>
              Prendre photo
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal Camera */}
      <Modal
        opened={isCameraVisible}
        onClose={() => setIsCameraVisible(false)}
        title={
          <Group>
            <Icon path={mdiCamera} size={1} />
            <Text fw={500}>Prendre photo</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            Fonctionnalité de prise de photo simple.
          </Text>
          <md-dialog-content class="layout-row layout-fill" cg-busy="vm.promise" id="dialogContent_264" style="position: relative;">
        <div class="md-dialog-content flex layout-row layout-align-center-center">

            <!-- ngIf: vm.error --><div class="error-content flex-nogrow layout-row layout-align-start-center ng-scope" ng-if="vm.error" style="">
                <h3 translate-once="webcam_capture_picture_cant_access_device">Impossible d’accéder au périphérique</h3>
            </div><!-- end ngIf: vm.error -->

            <div class="flex layout-row layout-align-center-center flux-controller ng-hide" ng-class="{'has-previews': vm.previews.length > 0, 'slide-left': vm.showPreview}" ng-show="vm.success" aria-hidden="true" style="">
                <video muted="" autoplay=""></video>
            </div>

            <div class="flex layout-row layout-align-center-center flux-controller preview ng-hide" md-swipe-left="vm.swipeLeft()" md-swipe-right="vm.swipeRight()" ng-class="{'has-previews': vm.previews.length > 0, 'slide-right': vm.showPreview}" ng-show="vm.success" aria-hidden="true" style="">
                <img>
            </div>

            <div class="action-container">
                <!-- ngIf: vm.showPreview -->
                <!-- ngIf: !vm.showPreview && vm.success -->
                <!-- ngIf: !vm.multi && vm.showPreview && !vm.cropper -->
                <!-- ngIf: vm.videoDevices && vm.videoDevices.length > 1 && !vm.showPreview -->
                <button class="md-fab mn-success md-button md-ink-ripple" type="button" ng-transclude="" ng-disabled="vm.items.length == 0 &amp;&amp; !vm.item" ng-click="vm.savePicture()" aria-label="switch cam" disabled="disabled">
                    <md-icon md-font-icon="mdi-check" md-font-set="mdi" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="true"></md-icon>
                </button>
                <button class="md-fab md-warn md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.cancel()" aria-label="switch cam">
                    <md-icon md-font-icon="mdi-close" md-font-set="mdi" class="ng-scope md-font mdi mdi-close" role="img" aria-hidden="true"></md-icon>
                </button>
            </div>
        </div>

        <div class="web-cam-thumbnail" ng-class="{opened: vm.previews.length > 0}">
            <!-- ngRepeat: item in vm.previews track by $index -->
        </div>
    <div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""></div><div class="cg-busy ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""><div class="cg-loader-spinner" style="position: absolute; inset: 0px;">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div></div></md-dialog-content>
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setIsCameraVisible(false)}>
              Annuler
            </Button>
            <Button leftSection={<IconCamera size={16} />}>
              Prendre photo
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal Folder Plus */}
      <Modal
        opened={isFolderPlusVisible}
        onClose={() => setIsFolderPlusVisible(false)}
        title={
          <Group>
            <Icon path={mdiFolderPlus} size={1} />
            <Text fw={500}>Nouveau dossier</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            Créer un nouveau dossier pour organiser vos fichiers.
          </Text>
         <md-content class="flex layout-column _md" ng-disabled="!vm.rootId" ngf-drop="vm.uploadFiles($files)" ngf-drag-over-class="'dragover'" ngf-multiple="true" cg-busy="vm.promise" aria-disabled="false">

    <!-- ngIf: (vm.files.length == 0) && !vm.parentFolder -->

    <div class="flex-nogrow layout-wrap ng-isolate-scope layout-column" layout="column" dragula="&quot;manager-bag&quot;" data-container="content">
        <!-- ngIf: vm.parentFolder && !vm.showFilterClear() -->

        <!-- ngRepeat: file in vm.files track by file.id --><mn-file-item ng-repeat="file in vm.files track by file.id" file="file" filters="vm.filters" show-type="vm.showType" open-file="vm.openFile(file, editImage, event)" remove-file="vm.remove(file)" download-file="vm.download(file)" ng-class="{'folder': file.mime =='folder', 'current-context': vm.checkContext(file)}" class="ng-scope ng-isolate-scope list-item folder current-context" data-file="685fe84d83b382b98580659c" style=""><md-list-item role="listitem" class="_md-button-wrap md-with-secondary ng-scope _md md-clickable" tabindex="-1" data-file="685fe84d83b382b98580659c"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.openFile({file: vm.file, event: $event, editImage: false})" aria-label="file-item"></button>   <div class="md-list-item-inner">

    <img alt="" ng-src="/img/folder.svg" class="md-avatar" src="/img/folder.svg">

    <span ng-hide="vm.editMode || vm.parent" class="layout flex item-container" aria-hidden="false">
        <span ng-bind="vm.file.name" class="flex file-title ng-binding">Nouveau dossier</span>
        <span ng-bind="vm.file['created_at']" class="file-upload-date ng-binding">28/06/2025 13:04</span>
    </span>

    <span ng-show="vm.parent" translate-once="file_manager_parent_folder" class="file-title ng-hide" aria-hidden="true">..</span>

    <input ng-model="vm.file.name" ng-show="vm.editMode" class="flex ng-pristine ng-untouched ng-valid ng-not-empty ng-hide" autocomplete="off" aria-hidden="true" aria-invalid="false">

    
    
    
    

</div><div class="md-secondary-container"><button class="md-secondary md-icon-button md-button md-ink-ripple ng-hide" type="button" ng-transclude="" ng-click="vm.cancelEdit()" aria-label="remove icon" ng-show="vm.editMode" aria-hidden="true"><md-icon class="md-secondary ng-scope md-font mdi mdi-window-close" md-font-icon="mdi-window-close" md-font-set="mdi" tabindex="-1" role="img" aria-hidden="true"></md-icon></button><button class="md-secondary md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.starEdit($event)" aria-label="edit icon" ng-hide="vm.parent || vm.editMode" aria-hidden="false"><md-icon class="md-secondary ng-scope md-font mdi mdi-pencil" md-font-icon="mdi-pencil" md-font-set="mdi" tabindex="-1" role="img" aria-hidden="true"></md-icon></button><!-- ngIf: vm.file.mime !='folder' --><button class="md-secondary md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.remove({file: vm.file})" aria-label="remove icon" ng-hide="vm.parent" aria-hidden="false"><md-icon class="md-secondary ng-scope md-font mdi mdi-delete" md-font-icon="mdi-delete" md-font-set="mdi" tabindex="-1" role="img" aria-hidden="true"></md-icon></button></div></div></md-list-item></mn-file-item><!-- end ngRepeat: file in vm.files track by file.id -->
    </div>
<div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""></div><div class="cg-busy ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""><div class="cg-loader-spinner" style="position: absolute; inset: 0px;">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div></div></md-content>
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setIsFolderPlusVisible(false)}>
              Annuler
            </Button>
            <Button leftSection={<IconFolderPlus size={16} />}>
              Créer dossier
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal File Tree */}
      <Modal
        opened={isFileTreeVisible}
        onClose={() => setIsFileTreeVisible(false)}
        title={
          <Group>
            <Icon path={mdiFileTree} size={1} />
            <Text fw={500}>Importer (interface)</Text>
          </Group>
        }
        centered
        size="md"
      >
       <md-dialog aria-label="exam search dialog" class="search-dialog _md md-transition-in" role="dialog" tabindex="-1" id="dialogContent_272" aria-describedby="dialogContent_272" style="">
    <div class="mn-search-modal-content">
        <md-toolbar class="_md _md-toolbar-transitions">
            <div class="md-toolbar-tools">
                <md-icon md-font-icon="mdi-folder-download" md-font-set="mdi" class="md-font mdi mdi-folder-download" role="img" aria-label="mdi-folder-download"></md-icon>
                <h2>
                    <span translate-once="imported_exams_list">Liste d'examens importés</span>
                    <!-- ngIf: vm.fullName --><span ng-if="vm.fullName" ng-bind="' (' + vm.fullName + ')'" class="ng-binding ng-scope" style=""> (ABDESSALMAD AGADIR)</span><!-- end ngIf: vm.fullName -->
                </h2>
                <span flex="" class="flex"></span>
                <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.clearQuery()" aria-label="clear query">
                    <md-icon md-font-icon="mdi-notification-clear-all" md-font-set="mdi" class="ng-scope md-font mdi mdi-notification-clear-all" role="img" aria-hidden="true"></md-icon>
                    
                </button>
                <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.cancel()" aria-label="Close dialog">
                    <md-icon md-font-icon="mdi-close" md-font-set="mdi" class="ng-scope md-font mdi mdi-close" role="img" aria-hidden="true"></md-icon>
                </button>
            </div>
        </md-toolbar>
        <md-content class="_md">
            <mn-table mn-model="interfacing.ReceivedData" tr-click="vm.selectExam" selectable="false" mn-query="vm.query" actions="vm.actions" order="-acquisition_date_time" class="ng-isolate-scope">
    <md-sidenav class="mn-table-side-nav md-sidenav-left md-closed flex-nogrow layout-column ng-isolate-scope _md" md-component-id="module-side-nav" md-is-locked-open="vm.toggle" tabindex="-1">
        <mn-table-side-nav flex="" mn-model="vm.mnModel" layout="column" columns="vm.columns" mn-filters="vm.filters" query="vm.query" reload-handler="vm.getData" style-rules="vm.styleRules" draft-rule="vm.draftRule" class="ng-isolate-scope layout-column flex"><md-tabs flex="" md-border-bottom="" cg-busy="vm.promise" class="ng-isolate-scope flex" style="position: relative;"><md-tabs-wrapper> <md-tab-data>
      <md-tab md-on-select="vm.formatTab(true)" class="ng-scope ng-isolate-scope">
      
      
  </md-tab>

  <md-tab md-on-select="vm.formatTab(false)" class="ng-scope ng-isolate-scope">
      
      
  </md-tab>
</md-tab-data> <!-- ngIf: $mdTabsCtrl.shouldPaginate --> <!-- ngIf: $mdTabsCtrl.shouldPaginate --> <md-tabs-canvas tabindex="0" ng-focus="$mdTabsCtrl.redirectFocus()" ng-class="{ 'md-paginated': $mdTabsCtrl.shouldPaginate, 'md-center-tabs': $mdTabsCtrl.shouldCenterTabs }" ng-keydown="$mdTabsCtrl.keydown($event)"> <md-pagination-wrapper ng-class="{ 'md-center-tabs': $mdTabsCtrl.shouldCenterTabs }" md-tab-scroll="$mdTabsCtrl.scroll($event)" role="tablist" aria-label="Use the left and right arrow keys to navigate between tabs" style="transform: translate(0px, 0px);"><!-- ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="0" class="md-tab md-active" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-266" md-tab-id="266" aria-selected="true" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" style="" aria-controls="tab-content-266">
          <span translate-once="custom_filter" class="ng-scope">Filtre avancé</span>
      </md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="-1" class="md-tab" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-267" md-tab-id="267" aria-selected="false" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" style="" aria-controls="tab-content-267">
          <span translate-once="style_rules" class="ng-scope">Règles de mise en forme</span>
      </md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --> <md-ink-bar></md-ink-bar> </md-pagination-wrapper> <md-tabs-dummy-wrapper aria-hidden="true" class="md-visually-hidden md-dummy-wrapper"> <!-- ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent" style="">
          <span translate-once="custom_filter" class="ng-scope">Filtre avancé</span>
      </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent" style="">
          <span translate-once="style_rules" class="ng-scope">Règles de mise en forme</span>
      </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --> </md-tabs-dummy-wrapper> </md-tabs-canvas> </md-tabs-wrapper> <md-tabs-content-wrapper ng-show="$mdTabsCtrl.hasContent &amp;&amp; $mdTabsCtrl.selectedIndex >= 0" class="_md" aria-hidden="false"> <!-- ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-266" class="_md ng-scope md-no-transition md-active" role="tabpanel" aria-labelledby="tab-item-266" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --><div md-tabs-template="::tab.template" md-connected-if="tab.isActive()" md-scope="::tab.parent" ng-if="$mdTabsCtrl.enableDisconnect || tab.shouldRender()" class="ng-scope ng-isolate-scope">
      <md-content flex="" layout="column" class="ng-scope _md layout-column flex">
           <mn-table-custom-filter flex="" mn-model="vm.mnModel" layout="column" columns="vm.columns" mn-filters="vm.mnFilters" query="vm.query" reload-handler="vm.reloadHandler" class="ng-isolate-scope layout-column flex"><div class="mn-filters layout-row flex" flex="" layout="row">
    <div class="filter-items md-whiteframe-z1 flex layout-column">
        <div class="filter-select flex-none layout-row layout-align-center-center">
            <md-select ng-model="vm.currentFilter" aria-label="current filter" placeholder="Aucun filtre Enregistré" ng-disabled="!vm.mnFilters || vm.mnFilters.length==0" ng-change="vm.handleFilterChange()" class="ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-empty" role="button" id="select_276" aria-disabled="true" disabled="disabled" aria-invalid="false"><md-select-value class="md-select-value md-select-placeholder" id="select_value_label_275" aria-hidden="true"><span>Aucun filtre Enregistré</span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container" aria-hidden="true" role="presentation" id="select_container_277">  <md-select-menu role="presentation" class="_md"><md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="_md" id="select_listbox_278">
                <!-- ngRepeat: filter in vm.mnFilters track by filter.id -->
            </md-content></md-select-menu></div></md-select>
        </div>
        <div class="filter-buttons flex-none layout-row">
            <span flex="" class="flex"></span>
            <button class="md-icon-button md-primary md-button md-ink-ripple" type="button" ng-transclude="" ng-disabled="!vm.checkFiltersExist()||!vm.currentFilter['id']" aria-label="save" ng-click="vm.submit(false)" disabled="disabled">
                <md-icon md-font-icon="mdi-find-replace" md-font-set="mdi" class="ng-scope md-font mdi mdi-find-replace" role="img" aria-hidden="true"></md-icon>
                
            </button>
            <button class="md-icon-button md-primary md-button md-ink-ripple" type="button" ng-transclude="" aria-label="save" ng-click="vm.submit()" ng-disabled="!vm.checkFiltersExist()" disabled="disabled">
                <md-icon md-font-icon="mdi-magnify-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-magnify-plus" role="img" aria-hidden="true"></md-icon>
                
            </button>
            <button class="md-icon-button md-warn md-button md-ink-ripple" type="button" ng-transclude="" ng-disabled="!vm.filterApplied" ng-click="vm.clearFilter()" aria-label="clear filter" disabled="disabled">
                <md-icon md-font-icon="mdi-filter-remove-outline" md-font-set="mdi" class="ng-scope md-font mdi mdi-filter-remove-outline" role="img" aria-hidden="true"></md-icon>
            </button>
            <button class="md-icon-button md-primary md-button md-ink-ripple" type="button" ng-transclude="" aria-label="save" ng-click="vm.applyFilter(); $mdMenu.open($event)" ng-disabled="!vm.checkFiltersExist()" disabled="disabled">
                <md-icon md-font-icon="mdi-filter-outline" md-font-set="mdi" class="ng-scope md-font mdi mdi-filter-outline" role="img" aria-hidden="true"></md-icon>
            </button>
        </div>
        <md-content flex="" class="_md flex">
            <md-list flex="" role="list" class="flex">
                <!-- ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope" style="">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Nom" class="ng-scope">Nom</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope" style="">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Prénom" class="ng-scope">Prénom</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope" style="">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Date d'acquisition" class="ng-scope">Date d'acquisition</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] --><md-divider ng-if="!$last &amp;&amp; !column['isFilter']" class="ng-scope"></md-divider><!-- end ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index --><div ng-repeat="column in vm.filterColumns track by  cc=$index" class="ng-scope" style="">
                    <mn-table-filter-item column="column" clear-filter="vm.clear" filter-applied="vm.filterApplied" filter-columns="vm.filterColumns" class="ng-isolate-scope"><div class="md-primary md-no-sticky md-subheader _md" role="heading" aria-level="2">  <div class="md-subheader-inner">    <div class="md-subheader-content">
    <md-checkbox style="margin-bottom:0;" ng-change="vm.checkFilter($event, vm.column)" ng-model="vm.column['isFilter']" aria-label="handle_filter" tabindex="0" type="checkbox" role="checkbox" class="md-auto-horizontal-margin ng-pristine ng-untouched ng-valid ng-scope ng-empty" aria-checked="false" aria-invalid="false"><div class="md-container md-ink-ripple" md-ink-ripple="" md-ink-ripple-checkbox=""><div class="md-icon"></div></div><div ng-transclude="" class="md-label">
        <span translate="Titre du Driver" class="ng-scope">Titre du Driver</span>
    </div></md-checkbox>
</div>  </div></div>
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type!='boolean' && (!vm.column['filter_fields'] || vm.column['filter_fields'].length==0) -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='date' && vm.column.operator=='$between' -->
<!-- ngIf: vm.column['isFilter'] && vm.column.type=='boolean' --></mn-table-filter-item>
                    <!-- ngRepeat: c in column['filter_fields'] track by i=$index -->
                    <!-- ngIf: !$last && !column['isFilter'] -->
                    <!-- ngIf: !$last &&  column['isFilter'] -->
                </div><!-- end ngRepeat: column in vm.filterColumns track by  cc=$index -->
            </md-list>
        </md-content>
    </div>
</div></mn-table-custom-filter>
      </md-content>
      </div><!-- end ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-267" class="_md ng-scope md-no-transition md-right" role="tabpanel" aria-labelledby="tab-item-267" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --> </md-tabs-content-wrapper><div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-hide ng-scope" ng-show="$cgBusyIsActive()" aria-hidden="true"></div><div class="cg-busy ng-hide ng-scope" ng-show="$cgBusyIsActive()" aria-hidden="true"><div class="cg-loader-spinner" style="position: absolute; inset: 0px;">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div></div></md-tabs>
</mn-table-side-nav>
    </md-sidenav>
    <div class="mn-module-side-content flex layout-column">
        <md-toolbar class="md-table-toolbar md-default flex-nogrow _md _md-toolbar-transitions">
            <div class="md-toolbar-tools">
                <button class="md-icon-button md-primary filter-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.handleToggle($event)" aria-label="advanced search" tabindex="-1">
                    <md-icon md-font-icon="mdi-filter-variant" md-font-set="mdi" class="ng-scope md-font mdi mdi-filter-variant" role="img" aria-hidden="true"></md-icon>
                    
                </button>

                <md-icon md-font-icon="mdi-magnify" md-font-set="mdi" class="md-font mdi mdi-magnify" role="img" aria-label="mdi-magnify"></md-icon>
                <div flex="50" layout="row" class="global-search layout-row flex-50">
                    <input ng-model="vm.query.search_all" translate-once-placeholder="search" ng-change="vm.getData()" mn-auto-focus="vm.focus" class="ng-pristine ng-valid ng-empty ng-touched" autocomplete="off" placeholder="Rechercher" aria-invalid="false" style="">
                </div>
                <span flex="" class="flex"></span>
                <!-- ngRepeat: action in ::vm.actions['multiple'] track by $index -->
                <!-- ngRepeat: action in vm.actions['dynamicMultiple'] track by action.label -->
                <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.getColumn(true)" aria-label="excel" tabindex="-1">
                    <md-icon md-font-icon="mdi-reload" md-font-set="mdi" class="ng-scope md-font mdi mdi-reload" role="img" aria-hidden="true"></md-icon>
                    
                </button>
                <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.exportExcel($event)" aria-label="excel" tabindex="-1">
                    <md-icon md-font-icon="mdi-file-excel" md-font-set="mdi" class="ng-scope md-font mdi mdi-file-excel" role="img" aria-hidden="true"></md-icon>
                    
                </button>
                <md-menu md-position-mode="target-right target" class="md-menu ng-scope _md">
                    <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="menu" ng-click="$mdMenu.open($event)" tabindex="-1" aria-haspopup="true" aria-expanded="false" aria-owns="menu_container_271">
                        <md-icon md-font-icon="mdi-dots-vertical" md-font-set="mdi" class="ng-scope md-font mdi mdi-dots-vertical" role="img" aria-hidden="true"></md-icon>
                    </button>
                    
                <div class="_md md-open-menu-container md-whiteframe-z2" id="menu_container_271" style="display: none;" aria-hidden="true"><md-menu-content width="3" class="mn-table-menu ng-isolate-scope" dragula="&quot;md-menu-one&quot;" dragula-model="vm.columns" role="menu">
                        <!-- ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope" style="">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem" disabled="disabled">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="patient_last_name" class="ng-scope">Nom</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem" disabled="disabled">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="patient_first_name" class="ng-scope">Prénom</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem" disabled="disabled">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="acquisition_date_time" class="ng-scope">Date d'acquisition</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label --><md-menu-item ng-repeat="column in vm.columns track by column.label" class="ng-scope">
                            <button class="md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.hideColumn(column)" md-prevent-menu-close="md-prevent-menu-close" aria-label="menu-item" ng-disabled="::column['is_required']" role="menuitem" disabled="disabled">
                                <md-icon md-font-icon="mdi-check" md-font-set="mdi" ng-show="column['is_shown']" class="ng-scope md-font mdi mdi-check" role="img" aria-hidden="false"></md-icon>

                                <!-- ngIf: column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once="default_config_title" class="ng-scope">Titre du Driver</span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->
                                <!-- ngIf: column.type == 'custom' -->

                            </button>
                        </md-menu-item><!-- end ngRepeat: column in vm.columns track by column.label -->
                    </md-menu-content></div></md-menu>
            </div>
        </md-toolbar>
        <md-table-container cg-busy="vm.tablePromise" class="flex-grow" style="position: relative;">
            <!-- ngIf: vm.columns --><table class="mn-striped md-table ng-scope ng-isolate-scope" md-table="" md-progress="vm.promise" multiple="true" ng-if="vm.columns" style="">
                <thead md-head="" md-order="vm.query.order" md-on-reorder="vm.onReorder" class="md-head ng-isolate-scope">
                <tr md-row="" class="md-row">
                    <!-- ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope md-sort" md-order-by="patient_last_name" style="">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="patient_last_name" class="ng-binding ng-scope" title="Nom">
                            Nom
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope md-sort" md-order-by="patient_first_name" style="">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="patient_first_name" class="ng-binding ng-scope" title="Prénom">
                            Prénom
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope md-active md-sort date-column" md-order-by="acquisition_date_time" style="">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="acquisition_date_time" class="ng-binding ng-scope" title="Date d'acquisition">
                            Date d'acquisition
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-desc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><th ng-if="column['is_shown']" md-column="" md-numeric="column.type == 'number'" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" ng-attr-md-order-by="{{::(column['is_orderable'] &amp;&amp; column['order_by'] || undefined)}}" ng-class="{'date-column': column.type == 'date', 'number-column': column.type == 'number', 'status-column':column.type == 'icon'}" class="md-column ng-scope ng-isolate-scope md-sort" md-order-by="default_config.title" style="">

                        <!-- ngIf: column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: !column['is_callable'] && column.type != 'custom' --><span ng-if="!column['is_callable'] &amp;&amp; column.type != 'custom'" translate-once-title="default_config_title" class="ng-binding ng-scope" title="Titre du Driver">
                            Titre du Driver
                            <!-- ngIf: column.description -->
                        </span><!-- end ngIf: !column['is_callable'] && column.type != 'custom' -->

                        <!-- ngIf: column.type == 'custom' -->
                    <md-icon md-svg-icon="arrow-up.svg" class="md-sort-icon ng-scope md-asc" ng-class="getDirection()" role="img" aria-label="arrow-up.svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"></path></svg></md-icon></th><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' -->

                    <!-- ngIf: ::(vm.actions['single'].length > 0) --><th md-column="" ng-if="::(vm.actions['single'].length > 0)" ng-class="'actions-column-' + vm.actions['single'].length" class="md-column ng-scope ng-isolate-scope actions-column-1"></th><!-- end ngIf: ::(vm.actions['single'].length > 0) -->
                </tr>
                </thead>
                <thead class="md-table-progress ng-isolate-scope" md-table-progress=""><tr>
  <th colspan="5">
    <md-progress-linear ng-show="deferred()" md-mode="indeterminate" aria-valuemin="0" aria-valuemax="100" role="progressbar" aria-hidden="true" class="ng-hide" style=""><div class="md-container md-mode-indeterminate"><div class="md-dashed"></div><div class="md-bar md-bar1"></div><div class="md-bar md-bar2"></div></div></md-progress-linear>
  </th>
</tr></thead><tbody md-body="" class="search-tbody md-body">
                <tr md-row="" class="search-tr md-row">
                    <!-- ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope" style="">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-not-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope" style="">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-not-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope" style="">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' --><!-- ngIf: column['is_shown'] --><td md-cell="" ng-if="column['is_shown']" ng-repeat="column in vm.columns track by column.label | orderBy:'order'" class="md-cell ng-scope" style="">
                        <!-- ngIf: column['is_searchable'] --><input ng-if="column['is_searchable']" type="text" translate-once-placeholder="search" ng-model="vm.query.search[column.order_by]" ng-change="vm.getData()" class="ng-pristine ng-untouched ng-valid ng-scope ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher"><!-- end ngIf: column['is_searchable'] -->
                    </td><!-- end ngIf: column['is_shown'] --><!-- end ngRepeat: column in vm.columns track by column.label | orderBy:'order' -->
                    <!-- ngIf: ::(vm.actions['single'].length > 0) --><td md-cell="" ng-if="::(vm.actions['single'].length > 0)" class="action-cell md-cell ng-scope"></td><!-- end ngIf: ::(vm.actions['single'].length > 0) -->
                </tr>
                </tbody>
                <tbody md-body="" class="md-body">
                <!-- ngIf: !vm.rowCollection || vm.rowCollection.length == 0 --><tr class="no-element md-row ng-scope" md-row="" ng-if="!vm.rowCollection || vm.rowCollection.length == 0">
                    <td md-cell="" colspan="5" class="md-cell">
                        <span translate-once="no_element_to_show">Aucun élément trouvé.</span>
                    </td>
                    <td class="ng-hide"></td>
                </tr><!-- end ngIf: !vm.rowCollection || vm.rowCollection.length == 0 -->
                <!-- ngRepeat: row in vm.rowCollection track by row.id -->
                </tbody>
            </table><!-- end ngIf: vm.columns -->
        <div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-hide ng-scope" ng-show="$cgBusyIsActive()" aria-hidden="true"></div><div class="cg-busy ng-hide ng-scope" ng-show="$cgBusyIsActive()" aria-hidden="true"><div class="cg-loader-spinner" style="position: absolute; inset: 0px;">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div></div></md-table-container>
        <md-table-pagination md-boundary-links="" md-page-select="" md-limit="vm.query.limit" md-page="vm.query.page" md-on-paginate="vm.onPaginate" md-total="0" class="flex-nogrow md-table-pagination ng-isolate-scope" md-limit-options="[5, 10, 15, 20]" md-label="{&quot;of&quot;:&quot;de&quot;,&quot;page&quot;:&quot;Page&quot;,&quot;rowsPerPage&quot;:&quot;Lignes par Page&quot;}"><!-- ngIf: $pagination.showPageSelect() --><div class="page-select ng-scope" ng-if="$pagination.showPageSelect()" style="">
  <div class="label ng-binding">Page</div>

  <md-select virtual-page-select="" total="1" class="md-table-select ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-empty" ng-model="$pagination.page" md-container-class="md-pagination-select" ng-change="$pagination.onPaginationChange()" ng-disabled="$pagination.disabled" aria-label="Page" tabindex="0" role="button" aria-haspopup="listbox" id="select_282" aria-invalid="false" aria-labelledby="select_282 select_value_label_281"><md-select-value class="md-select-value" id="select_value_label_281"><span><div class="md-text ng-binding">1</div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container md-pagination-select" aria-hidden="true" role="presentation" id="select_container_283">  <md-select-menu role="presentation" class="_md">
    <md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="_md" id="select_listbox_284">
      <!-- ngRepeat: page in $pageSelect.pages --><md-option ng-repeat="page in $pageSelect.pages" ng-value="page" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_289" value="1" selected="selected" aria-selected="true"><div class="md-text ng-binding">1</div></md-option><!-- end ngRepeat: page in $pageSelect.pages -->
    </md-content>
  </md-select-menu></div></md-select>
</div><!-- end ngIf: $pagination.showPageSelect() -->

<!-- ngIf: $pagination.limitOptions --><div class="limit-select ng-scope" ng-if="$pagination.limitOptions" style="">
  <div class="label ng-binding">Lignes par Page</div>

  <md-select class="md-table-select ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-empty" ng-model="$pagination.limit" md-container-class="md-pagination-select" ng-disabled="$pagination.disabled" aria-label="Rows" placeholder="5" tabindex="0" role="button" aria-haspopup="listbox" id="select_286" aria-invalid="false" aria-labelledby="select_286 select_value_label_285"><md-select-value class="md-select-value" id="select_value_label_285"><span><div class="md-text ng-binding">15</div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container md-pagination-select" aria-hidden="true" role="presentation" id="select_container_287">  <md-select-menu role="presentation" class="_md"><md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="_md" id="select_listbox_288">
    <!-- ngRepeat: option in $pagination.limitOptions --><md-option ng-repeat="option in $pagination.limitOptions" ng-value="option.value ? $pagination.eval(option.value) : option" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_290" value="5"><div class="md-text ng-binding">5</div></md-option><!-- end ngRepeat: option in $pagination.limitOptions --><md-option ng-repeat="option in $pagination.limitOptions" ng-value="option.value ? $pagination.eval(option.value) : option" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_291" value="10"><div class="md-text ng-binding">10</div></md-option><!-- end ngRepeat: option in $pagination.limitOptions --><md-option ng-repeat="option in $pagination.limitOptions" ng-value="option.value ? $pagination.eval(option.value) : option" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_292" value="15" selected="selected" aria-selected="true"><div class="md-text ng-binding">15</div></md-option><!-- end ngRepeat: option in $pagination.limitOptions --><md-option ng-repeat="option in $pagination.limitOptions" ng-value="option.value ? $pagination.eval(option.value) : option" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_293" value="20"><div class="md-text ng-binding">20</div></md-option><!-- end ngRepeat: option in $pagination.limitOptions -->
  </md-content></md-select-menu></div></md-select>
</div><!-- end ngIf: $pagination.limitOptions -->

<div class="buttons">
  <div class="label ng-binding">0 - 0 de 0</div>

  <!-- ngIf: $pagination.showBoundaryLinks() --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="$pagination.showBoundaryLinks()" ng-click="$pagination.first()" ng-disabled="$pagination.disabled || !$pagination.hasPrevious()" aria-label="First" disabled="disabled" style="">
    <md-icon md-svg-icon="navigate-first.svg" class="ng-scope" role="img" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M7 6 v12 h2 v-12 h-2z M17.41 7.41L16 6l-6 6 6 6 1.41-1.41L12.83 12z"></path></svg></md-icon>
  </button><!-- end ngIf: $pagination.showBoundaryLinks() -->

  <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="$pagination.previous()" ng-disabled="$pagination.disabled || !$pagination.hasPrevious()" aria-label="Previous" disabled="disabled">
    <md-icon md-svg-icon="navigate-before.svg" class="ng-scope" role="img" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"></path></svg></md-icon>
  </button>

  <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="$pagination.next()" ng-disabled="$pagination.disabled || !$pagination.hasNext()" aria-label="Next" disabled="disabled">
    <md-icon md-svg-icon="navigate-next.svg" class="ng-scope" role="img" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path></svg></md-icon>
  </button>

  <!-- ngIf: $pagination.showBoundaryLinks() --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="$pagination.showBoundaryLinks()" ng-click="$pagination.last()" ng-disabled="$pagination.disabled || !$pagination.hasNext()" aria-label="Last" disabled="disabled" style="">
    <md-icon md-svg-icon="navigate-last.svg" class="ng-scope" role="img" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fit="" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M15 6 v12 h2 v-12 h-2z M8 6L6.59 7.41 11.17 12l-4.58 4.59L8 18l6-6z"></path></svg></md-icon>
  </button><!-- end ngIf: $pagination.showBoundaryLinks() -->
</div></md-table-pagination>
    </div>
</mn-table>
        </md-content>
    </div>
</md-dialog>
      </Modal>

      {/* Modal Network */}
      <Modal
        opened={isNetworkVisible}
        onClose={() => setIsNetworkVisible(false)}
        title={
          <Group>
            <IconNetwork size={24} />
            <Text fw={500}>Importer DCM</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            Importer des fichiers DCM depuis le réseau.
          </Text>
          <div style={{
            padding: '20px',
            border: '2px dashed #ced4da',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <IconNetwork size={48} color="#868e96" />
            <Text mt="sm" c="dimmed">
              Import DCM réseau en développement
            </Text>
          </div>
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setIsNetworkVisible(false)}>
              Annuler
            </Button>
            <Button leftSection={<IconNetwork size={16} />}>
              Importer DCM
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
}
