'use client';
import React, { useState, useRef, useEffect } from 'react';
import { mdiCheck, mdiWindowClose } from '@mdi/js';
import CustomTable from './CustomTable'
import {
  ActionIcon,
  Group,
  ScrollArea,
  Stack,
  Text,
  Tooltip,
  FileInput,
  Divider,
  Loader,
  Card,
  Modal,
  Button,
  Box,
  Image,
  TextInput,
  Tabs,
  Table,
  Pagination,
  Checkbox
} from '@mantine/core';
import {
  IconFolder,
  IconCloudUpload,
  IconMicrophone,
  IconCamera,
  IconCameraPlus,
  IconFolderPlus,
  IconFileArrowRight,
  IconDownload,
  IconFilter,
  IconFilterOff,
  IconFile,
  IconTrash,
  IconNetwork,
} from '@tabler/icons-react';
import Icon from '@mdi/react';
import AudioRecorder from './AudioRecorder';
import {
  mdiFileTree,
  mdiCamera,
  mdiCameraPlus,
  mdiFolderPlus,
  mdiDownloadNetwork,
  mdiFileReplace,
  mdiLan,
  mdiLanConnect,
  mdiDownloadOutline,
  mdiSortCalendarAscending,
  mdiViewGrid,
  mdiFolderDownload,
  mdiNotificationClearAll,
  mdiClose,
  mdiMagnify,
  mdiFilter,
  mdiFormatListBulleted,
  mdiCalendar,
  mdiAccount
} from '@mdi/js';
type FilterColumn = {
  label: string;
  type: 'string' | 'number' | 'boolean' | 'date';
  operator?: string;
  isFilter: boolean;
  filter_fields?: string[];
};
type FileItem = {
  id: string;
  name: string;
  mime: string;
  created_at: string;
};
interface AttachmentManagerProps {
  patientId: string;
  onSave?: (blob: Blob, fileName: string) => void;
  onCancel?: () => void;
  isLoading?: boolean;
  success?: boolean;
  error?: boolean;
  showPreview?: boolean;
  previews?: string[]; // base64 or URL
  multipleDevices?: boolean;
  multi?: boolean;
  cropper?: boolean;
  videoDevices?: MediaDeviceInfo[];
  item?: File;
  items?: File[];
  files: FileItem[];
  parentFolder?: boolean;
  onOpenFile: (file: FileItem, editImage: boolean, event: React.MouseEvent) => void;
  onRemoveFile: (file: FileItem) => void;
  onDownloadFile: (file: FileItem) => void;
  onRenameFile: (file: FileItem, newName: string) => void;
  filterColumns: FilterColumn[];
  onFilterChange: (column: FilterColumn, checked: boolean) => void;
  clearFilter: () => void;
}

export default function AttachmentManager({
  patientId,
  onSave,
  onCancel,
  isLoading = false,
  success = false,
  error = false,
  showPreview = false,
  previews = [],
  item,
  items = [],
  files,
  parentFolder = false,

  onRemoveFile,
 
  onRenameFile,
    filterColumns,
  onFilterChange,

  
}: AttachmentManagerProps) {
  const [files, setFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(false);
  const [ignoreContext, setIgnoreContext] = useState(false);
  const [viewGrid, setViewGrid] = useState(true);
  const [sortedByDate, setSortedByDate] = useState(true);

  // États pour tous les modals
  const [isMicrophoneVisible, setIsMicrophoneVisible] = useState(false);
  const [isCameraVisible, setIsCameraVisible] = useState(false);
  const [isCameraPlusVisible, setIsCameraPlusVisible] = useState(false);
  const [isFolderPlusVisible, setIsFolderPlusVisible] = useState(false);
  const [isFileTreeVisible, setIsFileTreeVisible] = useState(false);
  const [isNetworkVisible, setIsNetworkVisible] = useState(false);

  const videoRef = useRef<HTMLVideoElement | null>(null);
  const [, setCameraSuccess] = useState(false);

  // Initialize webcam
  useEffect(() => {
    const startCamera = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          setCameraSuccess(true);
        }
      } catch (err) {
        console.error('Camera access error:', err);
        setCameraSuccess(false);
      }
    };
    startCamera();

    return () => {
      // Clean up on unmount
      const stream = videoRef.current?.srcObject as MediaStream | null;
      stream?.getTracks().forEach((track) => track.stop());
    };
  }, []);
  const handleUpload = (uploaded: File[]) => {
    setLoading(true);
    console.log(`Uploading files for patient: ${patientId}`, uploaded);
    setFiles(prev => [...prev, ...uploaded]);
    setTimeout(() => setLoading(false), 1500);
  };

  const handleAudioSave = (audioBlob: Blob, fileName: string) => {
    console.log('Audio saved:', fileName, audioBlob);

    // Créer un objet File à partir du Blob
    const audioFile = new File([audioBlob], fileName, {
      type: 'audio/webm',
      lastModified: Date.now()
    });

    // Ajouter le fichier audio à la liste des fichiers
    setFiles(prev => [...prev, audioFile]);

    // Appeler la fonction onSave si fournie dans les props
    if (onSave) {
      onSave(audioBlob, fileName);
    }

    // Fermer le modal
    setIsMicrophoneVisible(false);

    console.log(`Fichier audio "${fileName}" sauvegardé pour le patient ${patientId}`);
  };

  // Fonctions de toggle pour chaque modal
  const toggleMicrophone = () => {
    const newState = !isMicrophoneVisible;
    setIsMicrophoneVisible(newState);
    console.log('Microphone modal toggled:', newState);
  };

  const toggleCamera = () => {
    setIsCameraVisible(!isCameraVisible);
    console.log('Camera modal toggled:', !isCameraVisible);
  };

  const toggleCameraPlus = () => {
    setIsCameraPlusVisible(!isCameraPlusVisible);
    console.log('Camera Plus modal toggled:', !isCameraPlusVisible);
  };

  const toggleFolderPlus = () => {
    setIsFolderPlusVisible(!isFolderPlusVisible);
    console.log('Folder Plus modal toggled:', !isFolderPlusVisible);
  };

  const toggleFileTree = () => {
    setIsFileTreeVisible(!isFileTreeVisible);
    console.log('File Tree modal toggled:', !isFileTreeVisible);
  };

  const toggleNetwork = () => {
    setIsNetworkVisible(!isNetworkVisible);
    console.log('Network modal toggled:', !isNetworkVisible);
  };
const [editModeId, setEditModeId] = useState<string | null>(null);
  const [fileNames, setFileNames] = useState<Record<string, string>>({});

  const handleEditClick = (file: FileItem) => {
    setEditModeId(file.id);
    setFileNames((prev) => ({ ...prev, [file.id]: file.name }));
  };

  const handleCancelEdit = () => {
    setEditModeId(null);
  };

  const handleNameChange = (id: string, value: string) => {
    setFileNames((prev) => ({ ...prev, [id]: value }));
  };

  const handleSaveName = (file: FileItem) => {
    onRenameFile(file, fileNames[file.id]);
    setEditModeId(null);
  };

  return (
    <Stack>
      <Group justify="space-between">
        <Group>
          <IconFolder size={24} />
          <Text fw={600}>Pièces jointes</Text>
        </Group>
        <Group gap="xs">
          <Tooltip label="Uploader fichiers">
            <ActionIcon
              variant="light"
              onClick={() => document.getElementById('fileInput')?.click()}
            >
              <IconCloudUpload size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Enregistrer audio">
            <ActionIcon
              variant="light"
              color={isMicrophoneVisible ? "blue" : "gray"}
              onClick={(event) => {
                event.preventDefault();
                console.log('Microphone button clicked, current state:', isMicrophoneVisible);
                toggleMicrophone();
              }}
            >
              <IconMicrophone size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Prendre photo (compte)">
            <ActionIcon
              variant="light"
              color={isCameraPlusVisible ? "blue" : "gray"}
              onClick={toggleCameraPlus}
            >
              <IconCameraPlus size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Prendre photo">
            <ActionIcon
              variant="light"
              color={isCameraVisible ? "blue" : "gray"}
              onClick={toggleCamera}
            >
              <IconCamera size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Nouveau dossier">
            <ActionIcon
              variant="light"
              color={isFolderPlusVisible ? "blue" : "gray"}
              onClick={toggleFolderPlus}
            >
              <IconFolderPlus size={20} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Importer (interface)">
            <ActionIcon
              variant="light"
              color={isFileTreeVisible ? "blue" : "gray"}
              onClick={toggleFileTree}
            >
              <Icon path={mdiFileTree} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Importer DCM">
            <ActionIcon
              variant="light"
              color={isNetworkVisible ? "blue" : "gray"}
              onClick={toggleNetwork}
            >
              <Icon path={mdiLan} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="FirePACS study">
            <ActionIcon variant="light" disabled>
              <Icon path={mdiDownloadNetwork} size={1} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Importer dernière interface">
            <ActionIcon variant="light">
              <Icon path={mdiFileReplace} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Dernier DCM">
            <ActionIcon variant="light">
              <Icon path={mdiLanConnect} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Dernier FirePACS">
            <ActionIcon variant="light" disabled>
             <Icon path={mdiDownloadOutline} size={1} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Filtre contexte">
            <ActionIcon
              variant={ignoreContext ? 'default' : 'filled'}
              onClick={() => setIgnoreContext(!ignoreContext)}
            >
              {ignoreContext ? <IconFilterOff size={20} /> : <IconFilter size={20} />}
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Trier par date">
            <ActionIcon variant="light" onClick={() => setSortedByDate(!sortedByDate)}>
             <Icon path={mdiSortCalendarAscending} size={1} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Changer affichage">
            <ActionIcon variant="light" onClick={() => setViewGrid(!viewGrid)}>
              <Icon path={mdiViewGrid} size={1} />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Group>

      {/* File uploader (hidden) */}
      <FileInput
        id="fileInput"
        multiple
        onChange={handleUpload}
        accept="*"
        style={{ display: 'none' }}
      />

      {/* Content */}
      {loading ? (
        <Loader />
      ) : files.length === 0 ? (
        <Text>Aucun fichier trouvé</Text>
      ) : (
        <ScrollArea>
          <Stack gap="sm">
            {files.map((file, index) => (
              <Card key={index} withBorder padding="md">
                <Group justify="space-between">
                  <Group>
                    <IconFile size={24} />
                    <div>
                      <Text fw={500}>{file.name}</Text>
                      <Text size="sm" c="dimmed">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </Text>
                    </div>
                  </Group>
                  <Group gap="xs">
                    <ActionIcon variant="light" color="blue">
                      <IconDownload size={16} />
                    </ActionIcon>
                    <ActionIcon variant="light" color="red">
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Group>
              </Card>
            ))}
          </Stack>
        </ScrollArea>
      )}

      {/* Modal d'enregistrement audio avec AudioRecorder */}
      <Modal
        opened={isMicrophoneVisible}
        onClose={() => {
          console.log('Audio modal closing...');
          setIsMicrophoneVisible(false);
        }}
        title={
          <Group>
            <IconMicrophone size={24} />
            <Text fw={500}>Enregistrer un fichier audio</Text>
          </Group>
        }
        centered
        size="lg"
      >
        <AudioRecorder
          patientId={patientId}
          onSave={handleAudioSave}
          onCancel={() => {
            console.log('Audio recording cancelled');
            setIsMicrophoneVisible(false);
          }}
          maxDuration={300} // 5 minutes
        />
      </Modal>

      {/* Modal Camera Plus */}
      <Modal
        opened={isCameraPlusVisible}
        onClose={() => setIsCameraPlusVisible(false)}
        title={
          <Group>
            <Icon path={mdiCameraPlus} size={1} />
            <Text fw={500}>Prendre photo (compte)</Text>
          </Group>
        }
        centered
        size="md"
      >
        {/* <Stack gap="md">
          <Text size="sm" c="dimmed">
            Fonctionnalité de prise de photo avec comptage automatique.
          </Text>
          <div style={{
            padding: '20px',
            border: '2px dashed #ced4da',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <Icon path={mdiCameraPlus} size={2} color="#868e96" />
            <Text mt="sm" c="dimmed">
              Caméra avec comptage en développement
            </Text>
          </div>
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setIsCameraPlusVisible(false)}>
              Annuler
            </Button>
            <Button leftSection={<IconCameraPlus size={16} />}>
              Prendre photo
            </Button>
          </Group>
        </Stack> */}
         <Box style={{ position: 'relative' }}>

      <Group justify="center" style={{ height: '400px' }}>
        {error && (
          <Text color="red" size="lg">
            Impossible d’accéder au périphérique
          </Text>
        )}

        {success && !showPreview && (
          <video autoPlay muted style={{ maxWidth: '100%', borderRadius: '8px' }} />
        )}

        {success && showPreview && (
          <Image src={previews[0]} alt="Preview" width={300} radius="md" />
        )}
      </Group>

      <Group justify="center" mt="md">
        <Button
          color="green"
          leftSection={<Icon path={mdiCheck} size={1} />}
          onClick={onSave}
          disabled={previews.length === 0}
        >
          Valider
        </Button>

        <Button
          color="red"
          leftSection={<Icon path={mdiClose} size={1} />}
          onClick={onCancel}
        >
          Annuler
        </Button>
      </Group>

      {/* Thumbnails */}
      {previews.length > 0 && (
        <Group justify="center" mt="sm">
          {previews.map((src, index) => (
            <Image key={index} src={src} width={60} height={60} radius="sm" alt={`preview-${index}`} />
          ))}
        </Group>
      )}

      {/* Loader */}
      {isLoading && (
        <Box style={{ position: 'absolute', inset: 0, backgroundColor: 'rgba(255,255,255,0.7)', zIndex: 10 }}>
          <Group justify="center" style={{ height: '100%' }}>
            <Loader size="lg" />
          </Group>
        </Box>
      )}
    </Box>
      </Modal>

      {/* Modal Camera */}
      <Modal
        opened={isCameraVisible}
        onClose={() => setIsCameraVisible(false)}
        title={
          <Group>
            <Icon path={mdiCamera} size={1} />
            <Text fw={500}>Prendre photo</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Box style={{ position: 'relative' }}>
      <Stack align="center" justify="center" style={{ minHeight: 400 }}>
        {/* Error Display */}
        {error && (
          <Text color="red" size="lg">
            Impossible d’accéder au périphérique
          </Text>
        )}

        {/* Video Preview */}
        {success && !showPreview && (
          <video ref={videoRef} autoPlay muted style={{ maxWidth: '100%', borderRadius: '8px' }} />
        )}

        {/* Image Preview */}
        {success && showPreview && previews.length > 0 && (
          <Image src={previews[0]} alt="Preview" width={320} radius="md" />
        )}
      </Stack>

      {/* Action Buttons */}
      <Group justify="center" mt="md">
        <Button
          color="green"
          leftSection={<Icon path={mdiCheck} size={1} />}
          onClick={onSave}
          disabled={(items.length === 0 && !item)}
        >
          Enregistrer
        </Button>
        <Button
          color="red"
          leftSection={<Icon path={mdiClose} size={1} />}
          onClick={onCancel}
        >
          Annuler
        </Button>
      </Group>

      {/* Thumbnail previews */}
      {previews.length > 0 && (
        <Group justify="center" mt="sm" className="web-cam-thumbnail opened">
          {previews.map((src, index) => (
            <Image key={index} src={src} width={64} height={64} radius="sm" alt={`preview-${index}`} />
          ))}
        </Group>
      )}

      {/* Loader (busy) */}
      {isLoading && (
        <Box
          style={{
            position: 'absolute',
            inset: 0,
            backgroundColor: 'rgba(255,255,255,0.6)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000,
          }}
        >
          <Loader size="lg" />
        </Box>
      )}
    </Box>
      </Modal>

      {/* Modal Folder Plus */}
      <Modal
        opened={isFolderPlusVisible}
        onClose={() => setIsFolderPlusVisible(false)}
        title={
          <Group>
            <Icon path={mdiFolderPlus} size={1} />
            <Text fw={500}>Nouveau dossier</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Box
      onDragOver={(e) => e.preventDefault()}
      onDrop={(e) => {
        const files = Array.from(e.dataTransfer.files);
        console.log('Dropped files:', files);
      }}
      style={{ minHeight: 300, position: 'relative', padding: 16 }}
    >
      <Stack gap="sm">
        {files.map((file) => {
          const isFolder = file.mime === 'folder';
          const inEditMode = editModeId === file.id;

          return (
            <Group
              key={file.id}
              justify="apart"
              className={`list-item ${isFolder ? 'folder' : ''}`}
              style={{ border: '1px solid #eee', borderRadius: 8, padding: 12 }}
            >
              <Group>
                <Image src="/img/folder.svg" alt="icon" width={32} />
                {!inEditMode ? (
                  <Stack gap={0}>
                    <Text fw={500}>{file.name}</Text>
                    <Text size="xs" c="dimmed">
                      {file.created_at}
                    </Text>
                  </Stack>
                ) : (
                  <TextInput
                    value={fileNames[file.id]}
                    onChange={(e) => handleNameChange(file.id, e.currentTarget.value)}
                    onBlur={() => handleSaveName(file)}
                    autoFocus
                  />
                )}
              </Group>

              <Group gap={6}>
                {inEditMode ? (
                  <Button
                    variant="subtle"
                    color="gray"
                    onClick={handleCancelEdit}
                    aria-label="cancel edit"
                  >
                    <Icon path={mdiWindowClose} size={1} />
                  </Button>
                ) : (
                  <>
                    {!parentFolder && (
                      <Button
                        variant="subtle"
                        color="blue"
                        onClick={() => handleEditClick(file)}
                        aria-label="edit"
                      >
                        <Icon path={mdiPencil} size={1} />
                      </Button>
                    )}
                    <Button
                      variant="subtle"
                      color="red"
                      onClick={() => onRemoveFile(file)}
                      aria-label="delete"
                    >
                      <Icon path={mdiDelete} size={1} />
                    </Button>
                  </>
                )}
              </Group>
            </Group>
          );
        })}
      </Stack>

      {isLoading && (
        <Box
          style={{
            position: 'absolute',
            inset: 0,
            backgroundColor: 'rgba(255, 255, 255, 0.6)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 999,
          }}
        >
          <Loader />
        </Box>
      )}
    </Box>
      </Modal>

      {/* Modal File Tree */}
      <Modal
        opened={isFileTreeVisible}
        onClose={() => setIsFileTreeVisible(false)}
        title={
          <Group>
            <Icon path={mdiFileTree} size={1} />
            <Text fw={500}>Importer (interface)</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Stack gap="md">
          {/* En-tête du modal */}
          <Group justify="space-between" align="center">
            <Group>
              <Icon path={mdiFolderDownload} size={1} />
              <div>
                <Text fw={600} size="lg">Liste d&apos;examens importés</Text>
                <Text size="sm" c="dimmed">(ABDESSALMAD AGADIR)</Text>
              </div>
            </Group>

            <Group gap="xs">
              <Tooltip label="Effacer la recherche">
                <ActionIcon variant="light" onClick={() => console.log('Clear query')}>
                  <Icon path={mdiNotificationClearAll} size={0.8} />
                </ActionIcon>
              </Tooltip>

              <Tooltip label="Fermer">
                <ActionIcon variant="light" onClick={() => setIsFileTreeVisible(false)}>
                  <Icon path={mdiClose} size={0.8} />
                </ActionIcon>
              </Tooltip>
            </Group>
          </Group>

          {/* Interface de recherche et filtres */}
          <div style={{
            border: '1px solid #e9ecef',
            borderRadius: '8px',
            padding: '16px',
            backgroundColor: '#f8f9fa'
          }}>
            {/* Barre de recherche */}
            <Group mb="md">
              <Icon path={mdiMagnify} size={1} />
              <TextInput
                placeholder="Rechercher des examens..."
                style={{ flex: 1 }}
                leftSection={<Icon path={mdiMagnify} size={0.8} />}
              />
              <Button variant="light" leftSection={<Icon path={mdiFilter} size={0.8} />}>
                Filtres
              </Button>
            </Group>

            {/* Onglets de filtres */}
            <Tabs defaultValue="filters" mb="md">
              <Tabs.List>
                <Tabs.Tab value="filters" leftSection={<Icon path={mdiFilter} size={0.8} />}>
                  Filtre avancé
                </Tabs.Tab>
                <Tabs.Tab value="styles" leftSection={<Icon path={mdiFormatListBulleted} size={0.8} />}>
                  Règles de mise en forme
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value="filters" pt="md">
                <Stack gap="sm">
                  <Group>
                    <TextInput
                      placeholder="Nom du patient"
                      leftSection={<Icon path={mdiAccount} size={0.8} />}
                      style={{ flex: 1 }}
                    />
                    <TextInput
                      placeholder="Date d'acquisition"
                      leftSection={<Icon path={mdiCalendar} size={0.8} />}
                      style={{ flex: 1 }}
                    />
                  </Group>

                  <Group>
                    <Button variant="light" size="sm">Appliquer filtres</Button>
                    <Button variant="outline" size="sm">Réinitialiser</Button>
                  </Group>
                </Stack>
              </Tabs.Panel>

              <Tabs.Panel value="styles" pt="md">
                <Text c="dimmed">Règles de mise en forme en développement</Text>
              </Tabs.Panel>
            </Tabs>

            {/* Tableau des examens */}
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Nom</Table.Th>
                  <Table.Th>Prénom</Table.Th>
                  <Table.Th>Date d&apos;acquisition</Table.Th>
                  <Table.Th>Titre du Driver</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                <Table.Tr>
                  <Table.Td>AGADIR</Table.Td>
                  <Table.Td>ABDESSALMAD</Table.Td>
                  <Table.Td>28/06/2025</Table.Td>
                  <Table.Td>Examen radiologique</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <Button size="xs" variant="light">Sélectionner</Button>
                      <Button size="xs" variant="outline">Voir</Button>
                    </Group>
                  </Table.Td>
                </Table.Tr>
                <Table.Tr>
                  <Table.Td colSpan={5} style={{ textAlign: 'center', color: '#868e96' }}>
                    Aucun autre examen trouvé
                  </Table.Td>
                </Table.Tr>
              </Table.Tbody>
            </Table>

            {/* Pagination */}
            <Group justify="center" mt="md">
              <Pagination total={1} value={1} onChange={() => {}} />
            </Group>
          </div>
        </Stack>
       <Stack gap="sm" style={{ padding: 16 }}>
      {filterColumns.map((column, index) => {
        const showDivider =
          index < filterColumns.length - 1 && !column.isFilter;

        return (
          <React.Fragment key={index}>
            <Checkbox
              checked={column.isFilter}
              onChange={(event) =>
                onFilterChange(column, event.currentTarget.checked)
              }
              label={<Text>{column.label}</Text>}
              styles={{ label: { fontWeight: 500 } }}
            />

            {/* Conditionnel pour afficher du contenu de filtre supplémentaire */}
            {column.isFilter &&
              column.type !== 'boolean' &&
              (!column.filter_fields || column.filter_fields.length === 0) && (
                <Text size="sm" color="dimmed">
                  Champ de filtre texte...
                </Text>
              )}

            {/* Autres conditions (date, booléens, etc.) peuvent être ajoutées ici */}

            {showDivider && <Divider />}
          </React.Fragment>
        );
      })}
    </Stack>
  <CustomTable/>

  
      </Modal>

      {/* Modal Network */}
      <Modal
        opened={isNetworkVisible}
        onClose={() => setIsNetworkVisible(false)}
        title={
          <Group>
            <IconNetwork size={24} />
            <Text fw={500}>Importer DCM</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            Importer des fichiers DCM depuis le réseau.
          </Text>
          <div style={{
            padding: '20px',
            border: '2px dashed #ced4da',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <IconNetwork size={48} color="#868e96" />
            <Text mt="sm" c="dimmed">
              Import DCM réseau en développement
            </Text>
          </div>
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setIsNetworkVisible(false)}>
              Annuler
            </Button>
            <Button leftSection={<IconNetwork size={16} />}>
              Importer DCM
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
}
