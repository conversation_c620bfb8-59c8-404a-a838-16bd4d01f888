'use client';
import React, { useState } from 'react';
import { mdiCheck, mdiClose } from '@mdi/js';

import {
  ActionIcon,
  Group,
  ScrollArea,
  Stack,
  Text,
  Tooltip,
  FileInput,
  Divider,
  Loader,
  Card,
  Modal,
  Button,
  Box,

} from '@mantine/core';
import {
  IconFolder,
  IconCloudUpload,
  IconMicrophone,
  IconCamera,
  IconCameraPlus,
  IconFolderPlus,
  IconFileArrowRight,
  IconDownload,
  IconFilter,
  IconFilterOff,
  IconFile,
  IconTrash,
  IconNetwork,
} from '@tabler/icons-react';
import Icon from '@mdi/react';
import { 
  mdiFileTree, 
  mdiCamera, 
  mdiCameraPlus, 
  mdiFolderPlus,
  mdiDownloadNetwork,
  mdiFileReplace,
  mdiLan,
  mdiLanConnect,
  mdiDownloadOutline,
  mdiSortCalendarAscending,
  mdiViewGrid
} from '@mdi/js';

interface AttachmentManagerProps {
  patientId: string;
   onSave: () => void;
  onCancel: () => void;
  isLoading: boolean;
  success: boolean;
  error: boolean;
  showPreview: boolean;
  previews: string[]; // base64 or URL
  multipleDevices?: boolean;

  
  multi?: boolean;
  cropper?: boolean;
  videoDevices?: MediaDeviceInfo[];
  item?: any;
  items?: any[];
}

export default function AttachmentManager({ patientId, onSave,
  onCancel,
  isLoading,
  success,
  error,
  showPreview,
  previews,
  multipleDevices = false,
   onSave,
  onCancel,
  isLoading,
  error,
  showPreview,
  previews,
  multi = false,
  cropper = false,
  videoDevices = [],
  item,
  items = [],
 }: AttachmentManagerProps) {
  const [files, setFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(false);
  const [ignoreContext, setIgnoreContext] = useState(false);
  const [viewGrid, setViewGrid] = useState(true);
  const [sortedByDate, setSortedByDate] = useState(true);

  // États pour tous les modals
  const [isMicrophoneVisible, setIsMicrophoneVisible] = useState(false);
  const [isCameraVisible, setIsCameraVisible] = useState(false);
  const [isCameraPlusVisible, setIsCameraPlusVisible] = useState(false);
  const [isFolderPlusVisible, setIsFolderPlusVisible] = useState(false);
  const [isFileTreeVisible, setIsFileTreeVisible] = useState(false);
  const [isNetworkVisible, setIsNetworkVisible] = useState(false);
const [success, setSuccess] = useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);
   // Initialize webcam
  useEffect(() => {
    const startCamera = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          setSuccess(true);
        }
      } catch (err) {
        console.error('Camera access error:', err);
        setSuccess(false);
      }
    };
    startCamera();

    return () => {
      // Clean up on unmount
      const stream = videoRef.current?.srcObject as MediaStream | null;
      stream?.getTracks().forEach((track) => track.stop());
    };
  }, []);
  const handleUpload = (uploaded: File[]) => {
    setLoading(true);
    console.log(`Uploading files for patient: ${patientId}`, uploaded);
    setFiles(prev => [...prev, ...uploaded]);
    setTimeout(() => setLoading(false), 1500);
  };

  // Fonctions de toggle pour chaque modal
  const toggleMicrophone = () => {
    const newState = !isMicrophoneVisible;
    setIsMicrophoneVisible(newState);
    console.log('Microphone modal toggled:', newState);
  };

  const toggleCamera = () => {
    setIsCameraVisible(!isCameraVisible);
    console.log('Camera modal toggled:', !isCameraVisible);
  };

  const toggleCameraPlus = () => {
    setIsCameraPlusVisible(!isCameraPlusVisible);
    console.log('Camera Plus modal toggled:', !isCameraPlusVisible);
  };

  const toggleFolderPlus = () => {
    setIsFolderPlusVisible(!isFolderPlusVisible);
    console.log('Folder Plus modal toggled:', !isFolderPlusVisible);
  };

  const toggleFileTree = () => {
    setIsFileTreeVisible(!isFileTreeVisible);
    console.log('File Tree modal toggled:', !isFileTreeVisible);
  };

  const toggleNetwork = () => {
    setIsNetworkVisible(!isNetworkVisible);
    console.log('Network modal toggled:', !isNetworkVisible);
  };

  return (
    <Stack>
      <Group justify="space-between">
        <Group>
          <IconFolder size={24} />
          <Text fw={600}>Pièces jointes</Text>
        </Group>
        <Group gap="xs">
          <Tooltip label="Uploader fichiers">
            <ActionIcon
              variant="light"
              onClick={() => document.getElementById('fileInput')?.click()}
            >
              <IconCloudUpload size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Enregistrer audio">
            <ActionIcon
              variant="light"
              color={isMicrophoneVisible ? "blue" : "gray"}
              onClick={(event) => {
                event.preventDefault();
                console.log('Microphone button clicked, current state:', isMicrophoneVisible);
                toggleMicrophone();
              }}
            >
              <IconMicrophone size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Prendre photo (compte)">
            <ActionIcon
              variant="light"
              color={isCameraPlusVisible ? "blue" : "gray"}
              onClick={toggleCameraPlus}
            >
              <IconCameraPlus size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Prendre photo">
            <ActionIcon
              variant="light"
              color={isCameraVisible ? "blue" : "gray"}
              onClick={toggleCamera}
            >
              <IconCamera size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Nouveau dossier">
            <ActionIcon
              variant="light"
              color={isFolderPlusVisible ? "blue" : "gray"}
              onClick={toggleFolderPlus}
            >
              <IconFolderPlus size={20} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Importer (interface)">
            <ActionIcon
              variant="light"
              color={isFileTreeVisible ? "blue" : "gray"}
              onClick={toggleFileTree}
            >
              <Icon path={mdiFileTree} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Importer DCM">
            <ActionIcon
              variant="light"
              color={isNetworkVisible ? "blue" : "gray"}
              onClick={toggleNetwork}
            >
              <Icon path={mdiLan} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="FirePACS study">
            <ActionIcon variant="light" disabled>
              <Icon path={mdiDownloadNetwork} size={1} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Importer dernière interface">
            <ActionIcon variant="light">
              <Icon path={mdiFileReplace} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Dernier DCM">
            <ActionIcon variant="light">
              <Icon path={mdiLanConnect} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Dernier FirePACS">
            <ActionIcon variant="light" disabled>
             <Icon path={mdiDownloadOutline} size={1} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Filtre contexte">
            <ActionIcon
              variant={ignoreContext ? 'default' : 'filled'}
              onClick={() => setIgnoreContext(!ignoreContext)}
            >
              {ignoreContext ? <IconFilterOff size={20} /> : <IconFilter size={20} />}
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Trier par date">
            <ActionIcon variant="light" onClick={() => setSortedByDate(!sortedByDate)}>
             <Icon path={mdiSortCalendarAscending} size={1} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Changer affichage">
            <ActionIcon variant="light" onClick={() => setViewGrid(!viewGrid)}>
              <Icon path={mdiViewGrid} size={1} />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Group>

      {/* File uploader (hidden) */}
      <FileInput
        id="fileInput"
        multiple
        onChange={handleUpload}
        accept="*"
        style={{ display: 'none' }}
      />

      {/* Content */}
      {loading ? (
        <Loader />
      ) : files.length === 0 ? (
        <Text>Aucun fichier trouvé</Text>
      ) : (
        <ScrollArea>
          <Stack gap="sm">
            {files.map((file, index) => (
              <Card key={index} withBorder padding="md">
                <Group justify="space-between">
                  <Group>
                    <IconFile size={24} />
                    <div>
                      <Text fw={500}>{file.name}</Text>
                      <Text size="sm" c="dimmed">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </Text>
                    </div>
                  </Group>
                  <Group gap="xs">
                    <ActionIcon variant="light" color="blue">
                      <IconDownload size={16} />
                    </ActionIcon>
                    <ActionIcon variant="light" color="red">
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Group>
              </Card>
            ))}
          </Stack>
        </ScrollArea>
      )}

      {/* Modal d'enregistrement audio */}
      <Modal
        opened={isMicrophoneVisible}
        onClose={() => {
          console.log('Modal closing...');
          setIsMicrophoneVisible(false);
        }}
        title={
          <Group>
            <IconMicrophone size={24} />
            <Text fw={500}>Enregistrer un fichier audio</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            Cliquez sur le bouton ci-dessous pour commencer l&apos;enregistrement audio.
          </Text>

          <div style={{
            padding: '20px',
            border: '2px dashed #ced4da',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <IconMicrophone size={48} color="#868e96" />
            <Text mt="sm" c="dimmed">
              Fonctionnalité d&apos;enregistrement audio en développement
            </Text>
          </div>

          <Group justify="flex-end" mt="md">
            <Button
              variant="outline"
              onClick={() => {
                console.log('Cancel button clicked');
                setIsMicrophoneVisible(false);
              }}
            >
              Annuler
            </Button>
            <Button
              leftSection={<IconMicrophone size={16} />}
              onClick={() => {
                console.log('Record button clicked');
                setIsMicrophoneVisible(false);
              }}
            >
              Commencer l&apos;enregistrement
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal Camera Plus */}
      <Modal
        opened={isCameraPlusVisible}
        onClose={() => setIsCameraPlusVisible(false)}
        title={
          <Group>
            <Icon path={mdiCameraPlus} size={1} />
            <Text fw={500}>Prendre photo (compte)</Text>
          </Group>
        }
        centered
        size="md"
      >
        {/* <Stack gap="md">
          <Text size="sm" c="dimmed">
            Fonctionnalité de prise de photo avec comptage automatique.
          </Text>
          <div style={{
            padding: '20px',
            border: '2px dashed #ced4da',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <Icon path={mdiCameraPlus} size={2} color="#868e96" />
            <Text mt="sm" c="dimmed">
              Caméra avec comptage en développement
            </Text>
          </div>
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setIsCameraPlusVisible(false)}>
              Annuler
            </Button>
            <Button leftSection={<IconCameraPlus size={16} />}>
              Prendre photo
            </Button>
          </Group>
        </Stack> */}
         <Box style={{ position: 'relative' }}>

      <Group justify="center" style={{ height: '400px' }}>
        {error && (
          <Text color="red" size="lg">
            Impossible d’accéder au périphérique
          </Text>
        )}

        {success && !showPreview && (
          <video autoPlay muted style={{ maxWidth: '100%', borderRadius: '8px' }} />
        )}

        {success && showPreview && (
          <Image src={previews[0]} alt="Preview" width={300} radius="md" />
        )}
      </Group>

      <Group justify="center" mt="md">
        <Button
          color="green"
          leftSection={<Icon path={mdiCheck} size={1} />}
          onClick={onSave}
          disabled={previews.length === 0}
        >
          Valider
        </Button>

        <Button
          color="red"
          leftSection={<Icon path={mdiClose} size={1} />}
          onClick={onCancel}
        >
          Annuler
        </Button>
      </Group>

      {/* Thumbnails */}
      {previews.length > 0 && (
        <Group justify="center" mt="sm">
          {previews.map((src, index) => (
            <Image key={index} src={src} width={60} height={60} radius="sm" alt={`preview-${index}`} />
          ))}
        </Group>
      )}

      {/* Loader */}
      {isLoading && (
        <Box style={{ position: 'absolute', inset: 0, backgroundColor: 'rgba(255,255,255,0.7)', zIndex: 10 }}>
          <Group justify="center" style={{ height: '100%' }}>
            <Loader size="lg" />
          </Group>
        </Box>
      )}
    </Box>
      </Modal>

      {/* Modal Camera */}
      <Modal
        opened={isCameraVisible}
        onClose={() => setIsCameraVisible(false)}
        title={
          <Group>
            <Icon path={mdiCamera} size={1} />
            <Text fw={500}>Prendre photo</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Box style={{ position: 'relative' }}>
      <Stack align="center" justify="center" style={{ minHeight: 400 }}>
        {/* Error Display */}
        {error && (
          <Text color="red" size="lg">
            Impossible d’accéder au périphérique
          </Text>
        )}

        {/* Video Preview */}
        {success && !showPreview && (
          <video ref={videoRef} autoPlay muted style={{ maxWidth: '100%', borderRadius: '8px' }} />
        )}

        {/* Image Preview */}
        {success && showPreview && previews.length > 0 && (
          <Image src={previews[0]} alt="Preview" width={320} radius="md" />
        )}
      </Stack>

      {/* Action Buttons */}
      <Group position="center" mt="md">
        <Button
          color="green"
          leftIcon={<Icon path={mdiCheck} size={1} />}
          onClick={onSave}
          disabled={(items.length === 0 && !item)}
        >
          Enregistrer
        </Button>
        <Button
          color="red"
          leftIcon={<Icon path={mdiClose} size={1} />}
          onClick={onCancel}
        >
          Annuler
        </Button>
      </Group>

      {/* Thumbnail previews */}
      {previews.length > 0 && (
        <Group position="center" mt="sm" className="web-cam-thumbnail opened">
          {previews.map((src, index) => (
            <Image key={index} src={src} width={64} height={64} radius="sm" alt={`preview-${index}`} />
          ))}
        </Group>
      )}

      {/* Loader (busy) */}
      {isLoading && (
        <Box
          style={{
            position: 'absolute',
            inset: 0,
            backgroundColor: 'rgba(255,255,255,0.6)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000,
          }}
        >
          <Loader size="lg" />
        </Box>
      )}
    </Box>
      </Modal>

      {/* Modal Folder Plus */}
      <Modal
        opened={isFolderPlusVisible}
        onClose={() => setIsFolderPlusVisible(false)}
        title={
          <Group>
            <Icon path={mdiFolderPlus} size={1} />
            <Text fw={500}>Nouveau dossier</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            Créer un nouveau dossier pour organiser vos fichiers.
          </Text>
          <div style={{
            padding: '20px',
            border: '2px dashed #ced4da',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <Icon path={mdiFolderPlus} size={2} color="#868e96" />
            <Text mt="sm" c="dimmed">
              Création de dossier en développement
            </Text>
          </div>
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setIsFolderPlusVisible(false)}>
              Annuler
            </Button>
            <Button leftSection={<IconFolderPlus size={16} />}>
              Créer dossier
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal File Tree */}
      <Modal
        opened={isFileTreeVisible}
        onClose={() => setIsFileTreeVisible(false)}
        title={
          <Group>
            <Icon path={mdiFileTree} size={1} />
            <Text fw={500}>Importer (interface)</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            Importer des fichiers via l&apos;interface système.
          </Text>
          <div style={{
            padding: '20px',
            border: '2px dashed #ced4da',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <Icon path={mdiFileTree} size={2} color="#868e96" />
            <Text mt="sm" c="dimmed">
              Import d&apos;interface en développement
            </Text>
          </div>
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setIsFileTreeVisible(false)}>
              Annuler
            </Button>
            <Button leftSection={<IconFileArrowRight size={16} />}>
              Importer
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal Network */}
      <Modal
        opened={isNetworkVisible}
        onClose={() => setIsNetworkVisible(false)}
        title={
          <Group>
            <IconNetwork size={24} />
            <Text fw={500}>Importer DCM</Text>
          </Group>
        }
        centered
        size="md"
      >
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            Importer des fichiers DCM depuis le réseau.
          </Text>
          <div style={{
            padding: '20px',
            border: '2px dashed #ced4da',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <IconNetwork size={48} color="#868e96" />
            <Text mt="sm" c="dimmed">
              Import DCM réseau en développement
            </Text>
          </div>
          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setIsNetworkVisible(false)}>
              Annuler
            </Button>
            <Button leftSection={<IconNetwork size={16} />}>
              Importer DCM
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
}
