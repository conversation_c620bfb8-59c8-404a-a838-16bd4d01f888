import {
  ActionIcon,
  Group,
  ScrollArea,
  Stack,
  Text,
  Tooltip,
  FileInput,
  
  Divider,
  Loader,
  
} from '@mantine/core';
import {
  IconFolder,
  IconCloudUpload,
  IconMicrophone,
  IconCamera,
  IconCameraPlus,
  IconFolderPlus,

 
  IconFileArrowRight,
 
  IconDownload,
  IconFilter,
  IconFilterOff,
  IconSortAscending,
  IconLayoutGrid,
} from '@tabler/icons-react';
import Icon from '@mdi/react';
import { mdiFileTree } from '@mdi/js';

import { useState } from 'react';

interface AttachmentManagerProps {
  patientId: string;
}

export default function AttachmentManager({ patientId }: AttachmentManagerProps) {
  const [files, ] = useState<File[]>([]);
  const [loading, setLoading] = useState(false);
  const [ignoreContext, setIgnoreContext] = useState(false);
  const [viewGrid, setViewGrid] = useState(true);
  const [sortedByDate, setSortedByDate] = useState(true);

  const handleUpload = (uploaded: File[]) => {
    setLoading(true);
    // TODO: upload files
    setTimeout(() => setLoading(false), 1500);
  };
const [isMicrophoneVisible, setIsMicrophoneVisible] = useState(false); // State to control sidebar visibility
  const toggleMicrophone = () => {
         const newState = !isMicrophoneVisible;
         setIsMicrophoneVisible(newState);
         // Si on ouvre Antécédents, fermer les autres
         if (newState) {
           setIsMicrophoneVisible(false);
           //setIsAllergiesVisible(false);
         }
       };

  return (
    <Stack>
      <Group justify="space-between">
        <Group>
          <IconFolder size={24} />
          <Text fw={600}>Pièces jointes</Text>
        </Group>
        <Group gap="xs">
          <Tooltip label="Uploader fichiers">
            <ActionIcon
              variant="light"
              onClick={() => document.getElementById('fileInput')?.click()}
            >
              <IconCloudUpload size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Enregistrer audio">
            <ActionIcon variant="light" 
             onClick={(event) => {
          event.preventDefault();
          toggleMicrophone();
        }}>
              <IconMicrophone size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Prendre photo (compte)">
            <ActionIcon variant="light">
              <IconCameraPlus size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Prendre photo">
            <ActionIcon variant="light">
              <IconCamera size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Nouveau dossier">
            <ActionIcon variant="light">
              <IconFolderPlus size={20} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Importer (interface)">
            <ActionIcon variant="light">
             
<Icon path={mdiFileTree} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Importer DCM">
            <ActionIcon variant="light">
              <IconLan size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="FirePACS study">
            <ActionIcon variant="light" disabled>
              <IconDownloadNetwork size={20} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Importer dernière interface">
            <ActionIcon variant="light">
              <IconFileArrowRight size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Dernier DCM">
            <ActionIcon variant="light">
              <IconLanConnect size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Dernier FirePACS">
            <ActionIcon variant="light" disabled>
              <IconDownload size={20} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Filtre contexte">
            <ActionIcon
              variant={ignoreContext ? 'default' : 'filled'}
              onClick={() => setIgnoreContext(!ignoreContext)}
            >
              {ignoreContext ? <IconFilterOff size={20} /> : <IconFilter size={20} />}
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Trier par date">
            <ActionIcon variant="light" onClick={() => setSortedByDate(!sortedByDate)}>
              <IconSortAscending size={20} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Changer affichage">
            <ActionIcon variant="light" onClick={() => setViewGrid(!viewGrid)}>
              <IconLayoutGrid size={20} />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Group>

      {/* File uploader (hidden) */}
      <FileInput
        id="fileInput"
        multiple
        onChange={handleUpload}
        accept="*"
        style={{ display: 'none' }}
      />

      {/* Content */}
      {loading ? (
        <Loader />
      ) : files.length === 0 ? (
        <Text>Aucun fichier trouvé</Text>
      ) : (
        <ScrollArea>
          {isMicrophoneVisible &&(
            <div><md-dialog md-theme="default" aria-label="prompt" class="audio-record-dialog _md md-default-theme md-transition-in" role="dialog" tabindex="-1" aria-describedby="dialogContent_261" style="">

        <md-dialog-content class="md-dialog-content" role="document" tabindex="-1" id="dialogContent_261">
            <h2 class="md-title" translate-once="file_manager_record_audio">Enregistrer un fichier audio</h2>

            <!-- ngIf: !vm.error -->

            <div class="layout-row ng-hide" ng-show="vm.showPreview" aria-hidden="true" style="">
                <audio class="flex" controls="true"></audio>
            </div>

            <!-- ngIf: vm.error --><div ng-if="vm.error" class="md-dialog-content-body ng-scope" style="">
                <p translate-once="file_manager_record_audio_error">Impossible de se connecter avec le microphone</p>
            </div><!-- end ngIf: vm.error -->

        </md-dialog-content>

        <md-dialog-actions>
            <button class="md-primary md-confirm-button md-button md-default-theme md-ink-ripple" type="submit" ng-transclude="" ng-click="vm.saveFile()" ng-disabled="vm.hasNoFile" aria-label="submit" disabled="disabled">
                <span translate-once="save" class="ng-scope">Enregistrer</span>
            </button>

            <button class="md-warn md-cancel-button md-button md-default-theme md-ink-ripple" type="button" ng-transclude="" ng-click="vm.abort()" aria-label="abort">
                <span translate-once="cancel" class="ng-scope">Annuler</span>
            </button>
        </md-dialog-actions>

</md-dialog></div>
            )}
        </ScrollArea>
      )}
    </Stack>
  );
}
