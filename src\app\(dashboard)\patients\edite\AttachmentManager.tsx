import {
  ActionIcon,
  Group,
  ScrollArea,
  Stack,
  Text,
  Tooltip,
  FileInput,
  Divider,
  Loader,
  Card,
  Modal,
  Button,
} from '@mantine/core';
import {
  IconFolder,
  IconCloudUpload,
  IconMicrophone,
  IconCamera,
  IconCameraPlus,
  IconFolderPlus,
  IconFileArrowRight,
  IconDownload,
  IconFilter,
  IconFilterOff,
  IconSortAscending,
  IconLayoutGrid,
  IconFile,
  IconTrash,
  IconNetwork,
} from '@tabler/icons-react';
import Icon from '@mdi/react';
import { mdiFileTree } from '@mdi/js';

import { useState } from 'react';

interface AttachmentManagerProps {
  patientId: string;
}

export default function AttachmentManager({ patientId }: AttachmentManagerProps) {
  const [files, setFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(false);
  const [ignoreContext, setIgnoreContext] = useState(false);
  const [viewGrid, setViewGrid] = useState(true);
  const [sortedByDate, setSortedByDate] = useState(true);

  const handleUpload = (uploaded: File[]) => {
    setLoading(true);
    // TODO: upload files for patient ID: ${patientId}
    console.log(`Uploading files for patient: ${patientId}`, uploaded);
    setFiles(prev => [...prev, ...uploaded]);
    setTimeout(() => setLoading(false), 1500);
  };
  const [isMicrophoneVisible, setIsMicrophoneVisible] = useState(false); // State to control modal visibility

  const toggleMicrophone = () => {
    const newState = !isMicrophoneVisible;
    setIsMicrophoneVisible(newState);
    console.log('Microphone modal toggled:', newState); // Debug log
  };

  return (
    <Stack>
      <Group justify="space-between">
        <Group>
          <IconFolder size={24} />
          <Text fw={600}>Pièces jointes</Text>
        </Group>
        <Group gap="xs">
          <Tooltip label="Uploader fichiers">
            <ActionIcon
              variant="light"
              onClick={() => document.getElementById('fileInput')?.click()}
            >
              <IconCloudUpload size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Enregistrer audio">
            <ActionIcon
              variant="light"
              color={isMicrophoneVisible ? "blue" : "gray"}
              onClick={(event) => {
                event.preventDefault();
                console.log('Microphone button clicked, current state:', isMicrophoneVisible);
                toggleMicrophone();
              }}
            >
              <IconMicrophone size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Prendre photo (compte)">
            <ActionIcon variant="light">
              <IconCameraPlus size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Prendre photo">
            <ActionIcon variant="light">
              <IconCamera size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Nouveau dossier">
            <ActionIcon variant="light">
              <IconFolderPlus size={20} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Importer (interface)">
            <ActionIcon variant="light">
             
<Icon path={mdiFileTree} size={1} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Importer DCM">
            <ActionIcon variant="light">
              <IconNetwork size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="FirePACS study">
            <ActionIcon variant="light" disabled>
              <IconNetwork size={20} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Importer dernière interface">
            <ActionIcon variant="light">
              <IconFileArrowRight size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Dernier DCM">
            <ActionIcon variant="light">
              <IconNetwork size={20} />
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Dernier FirePACS">
            <ActionIcon variant="light" disabled>
              <IconDownload size={20} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Filtre contexte">
            <ActionIcon
              variant={ignoreContext ? 'default' : 'filled'}
              onClick={() => setIgnoreContext(!ignoreContext)}
            >
              {ignoreContext ? <IconFilterOff size={20} /> : <IconFilter size={20} />}
            </ActionIcon>
          </Tooltip>

          <Tooltip label="Trier par date">
            <ActionIcon variant="light" onClick={() => setSortedByDate(!sortedByDate)}>
              <IconSortAscending size={20} />
            </ActionIcon>
          </Tooltip>

          <Divider orientation="vertical" />

          <Tooltip label="Changer affichage">
            <ActionIcon variant="light" onClick={() => setViewGrid(!viewGrid)}>
              <IconLayoutGrid size={20} />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Group>

      {/* File uploader (hidden) */}
      <FileInput
        id="fileInput"
        multiple
        onChange={handleUpload}
        accept="*"
        style={{ display: 'none' }}
      />

      {/* Content */}
      {loading ? (
        <Loader />
      ) : files.length === 0 ? (
        <Text>Aucun fichier trouvé</Text>
      ) : (
        <ScrollArea>
          {/* Liste des fichiers */}
          <Stack gap="sm">
            {files.map((file, index) => (
              <Card key={index} withBorder padding="md">
                <Group justify="space-between">
                  <Group>
                    <IconFile size={24} />
                    <div>
                      <Text fw={500}>{file.name}</Text>
                      <Text size="sm" c="dimmed">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </Text>
                    </div>
                  </Group>
                  <Group gap="xs">
                    <ActionIcon variant="light" color="blue">
                      <IconDownload size={16} />
                    </ActionIcon>
                    <ActionIcon variant="light" color="red">
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Group>
              </Card>
            ))}
          </Stack>

          {/* Modal d'enregistrement audio moderne */}
          <Modal
            opened={isMicrophoneVisible}
            onClose={() => {
              console.log('Modal closing...');
              setIsMicrophoneVisible(false);
            }}
            title={
              <Group>
                <IconMicrophone size={24} />
                <Text fw={500}>Enregistrer un fichier audio</Text>
              </Group>
            }
            centered
            size="md"
          >
            <Stack gap="md">
              <Text size="sm" c="dimmed">
                Cliquez sur le bouton ci-dessous pour commencer l&apos;enregistrement audio.
              </Text>

              <div style={{
                padding: '20px',
                border: '2px dashed #ced4da',
                borderRadius: '8px',
                textAlign: 'center'
              }}>
                <IconMicrophone size={48} color="#868e96" />
                <Text mt="sm" c="dimmed">
                  Fonctionnalité d&apos;enregistrement audio en développement
                </Text>
              </div>

              <Group justify="flex-end" mt="md">
                <Button
                  variant="outline"
                  onClick={() => {
                    console.log('Cancel button clicked');
                    setIsMicrophoneVisible(false);
                  }}
                >
                  Annuler
                </Button>
                <Button
                  leftSection={<IconMicrophone size={16} />}
                  onClick={() => {
                    console.log('Record button clicked');
                    // Ici on ajouterait la logique d'enregistrement
                    setIsMicrophoneVisible(false);
                  }}
                >
                  Commencer l&apos;enregistrement
                </Button>
              </Group>
            </Stack>
          </Modal>
        </ScrollArea>
      )}
    </Stack>
  );
}
