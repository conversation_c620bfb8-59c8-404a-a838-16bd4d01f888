// Types partagés pour les composants patients

export interface Patient {
  id: string;
  full_name: string;
  gender: string;
  age: number;
  default_insurance: string;
  file_number: string;
  last_visit?: string;
  file_date?: string;
}

export interface Measurement {
  id: string;
  date: string;
  values: { [label: string]: number | string | boolean | null };
  comment?: string;
}

export interface MeasureDefinition {
  id: string;
  label: string;
  type: 'float' | 'integer' | 'boolean' | 'string' | 'date' | 'calculated';
}

export interface Attachment {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadDate: string;
  description?: string;
  url?: string;
  category: 'medical' | 'administrative' | 'image' | 'other';
}

export interface PatientActionsProps {
  patientId?: string;
  isFormInvalid: boolean;
  isDraft: boolean;
  onPrint?: () => void;
  onPrevious?: () => void;
  onNext?: () => void;
  onStartVisit?: () => void;
  onAppointment?: () => void;
  onCancel?: () => void;
  onSaveQuitNew?: () => void;
  onSaveQuit?: () => void;
  onSubmit?: () => void;
  patient: Patient;
  onGoBack: () => void;
  onAddMeasurement?: () => void;
  onGoToContract?: () => void;
  measures?: MeasureDefinition[];
  measurements?: Measurement[];
}

export interface TreeNodeData {
  label: string;
  value: string;
  children?: TreeNodeData[];
}

export interface ChipData {
  id?: string;
  label: string;
}

export interface HistoryEntry {
  date: string;
  content: string;
}

export interface ModelData {
  id: string;
  name: string;
  content: string;
}
