'use client';
import React from 'react';
import { Group, Text, Textarea, ActionIcon } from '@mantine/core';
import Icon from '@mdi/react';
import { mdiPlusBox, mdiHistory, mdiMicrophone, mdiClipboardText, mdiDeleteSweep, mdiTagPlus } from '@mdi/js';
import { TreeNodeData } from '@mantine/core';

interface MedicalSectionProps {
  title: string;
  placeholder?: string;
  textareaContent: string;
  onTextareaChange: (value: string) => void;
  onTextareaClick: (event: React.MouseEvent) => void;
  onAddNew?: () => void;
  onHistoryClick?: () => void;
  onMicrophoneClick?: () => void;
  onModelsClick?: () => void;
  onDeleteClick?: () => void;
  onTagPlusClick?: () => void;
  showAddIcon?: boolean;
  isVisible: boolean;
  data?: TreeNodeData[];
}

export const MedicalSection: React.FC<MedicalSectionProps> = ({
  title,
  placeholder = "Ajouter",
  textareaContent,
  onTextareaChange,
  onTextareaClick,
  onAddNew,
  onHistoryClick,
  onMicrophoneClick,
  onModelsClick,
  onDeleteClick,
  onTagPlusClick,
  showAddIcon = false,
  isVisible
}) => {
  return (
    <div className={isVisible ? 'w-[49%]' : 'w-[49%]'}>
      <Group justify="space-between" gap="sm">
        <Group gap="sm">
          <Text fw={500}>{title}</Text>
        </Group>
        <Group justify="flex-end" gap="sm">
          {showAddIcon && onAddNew && (
            <ActionIcon variant="transparent" onClick={onAddNew} title="Ajouter à la liste">
              <Icon path={mdiPlusBox} size={1} color={"#3799ce"} />
            </ActionIcon>
          )}
          {onHistoryClick && (
            <ActionIcon variant="transparent" onClick={onHistoryClick}>
              <Icon path={mdiHistory} size={1} color={"#3799ce"} />
            </ActionIcon>
          )}
          {onMicrophoneClick && (
            <ActionIcon variant="transparent" onClick={onMicrophoneClick}>
              <Icon path={mdiMicrophone} size={1} color={"#3799ce"} />
            </ActionIcon>
          )}
          {onModelsClick && (
            <ActionIcon variant="transparent" onClick={onModelsClick}>
              <Icon path={mdiClipboardText} size={1} color={"#3799ce"} />
            </ActionIcon>
          )}
          {onTagPlusClick && (
            <ActionIcon variant="transparent" onClick={onTagPlusClick}>
              <Icon path={mdiTagPlus} size={1} color={"#3799ce"} />
            </ActionIcon>
          )}
          {onDeleteClick && (
            <ActionIcon variant="transparent" onClick={onDeleteClick}>
              <Icon path={mdiDeleteSweep} size={1} color={"#e53935"} />
            </ActionIcon>
          )}
        </Group>
      </Group>
      <Textarea
        size="xl"
        radius="md"
        placeholder={placeholder}
        mt={10}
        mb={10}
        value={textareaContent}
        onChange={(event) => onTextareaChange(event.currentTarget.value)}
        onClick={(event) => onTextareaClick(event)}
      />
    </div>
  );
};
