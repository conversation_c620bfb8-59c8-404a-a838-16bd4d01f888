'use client';
import React, { useState } from 'react';
import { <PERSON><PERSON>, Stack, Text, Card, Group, Badge, Alert } from '@mantine/core';
import { IconMicrophone, IconCheck, IconAlertCircle } from '@tabler/icons-react';
import AudioRecorder from './AudioRecorder';

interface AudioFile {
  name: string;
  size: number;
  type: string;
  url: string;
  createdAt: Date;
}

export const AudioRecorderTest: React.FC = () => {
  const [isRecorderOpen, setIsRecorderOpen] = useState(false);
  const [savedAudioFiles, setSavedAudioFiles] = useState<AudioFile[]>([]);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const handleAudioSave = (audioBlob: Blob, fileName: string) => {
    try {
      // Créer une URL pour le blob audio
      const audioUrl = URL.createObjectURL(audioBlob);
      
      // Créer l'objet fichier audio
      const audioFile: AudioFile = {
        name: fileName,
        size: audioBlob.size,
        type: audioBlob.type,
        url: audioUrl,
        createdAt: new Date()
      };

      // Ajouter à la liste des fichiers sauvegardés
      setSavedAudioFiles(prev => [...prev, audioFile]);
      
      // Ajouter un résultat de test
      addTestResult(`✅ Fichier audio sauvegardé: ${fileName} (${(audioBlob.size / 1024).toFixed(2)} KB)`);
      
      // Fermer le recorder
      setIsRecorderOpen(false);
      
      console.log('Audio file saved successfully:', audioFile);
    } catch (error) {
      addTestResult(`❌ Erreur lors de la sauvegarde: ${error}`);
      console.error('Error saving audio file:', error);
    }
  };

  const handleAudioCancel = () => {
    addTestResult('⚠️ Enregistrement annulé par l\'utilisateur');
    setIsRecorderOpen(false);
  };

  const downloadAudioFile = (audioFile: AudioFile) => {
    try {
      const a = document.createElement('a');
      a.href = audioFile.url;
      a.download = audioFile.name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      
      addTestResult(`📥 Téléchargement initié: ${audioFile.name}`);
    } catch (error) {
      addTestResult(`❌ Erreur de téléchargement: ${error}`);
    }
  };

  const deleteAudioFile = (index: number) => {
    const audioFile = savedAudioFiles[index];
    
    // Libérer l'URL du blob
    URL.revokeObjectURL(audioFile.url);
    
    // Supprimer de la liste
    setSavedAudioFiles(prev => prev.filter((_, i) => i !== index));
    
    addTestResult(`🗑️ Fichier supprimé: ${audioFile.name}`);
  };

  const clearTestResults = () => {
    setTestResults([]);
  };

  const runPermissionTest = async () => {
    try {
      addTestResult('🔍 Test des permissions microphone...');
      
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      addTestResult('✅ Permissions microphone accordées');
      
      // Arrêter le stream de test
      stream.getTracks().forEach(track => track.stop());
      
      // Tester les capacités d'enregistrement
      if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
        addTestResult('✅ Format audio/webm;codecs=opus supporté');
      } else if (MediaRecorder.isTypeSupported('audio/webm')) {
        addTestResult('✅ Format audio/webm supporté');
      } else {
        addTestResult('⚠️ Formats audio limités');
      }
      
    } catch (error) {
      addTestResult(`❌ Erreur permissions: ${error}`);
    }
  };

  return (
    <Stack gap="md" p="md">
      <Card withBorder padding="lg">
        <Stack gap="md">
          <Group justify="space-between">
            <Text size="xl" fw={700}>Test AudioRecorder</Text>
            <Badge color="blue">Version 1.0</Badge>
          </Group>
          
          <Text c="dimmed">
            Interface de test pour le composant d&apos;enregistrement audio.
            Testez l&apos;enregistrement, la lecture, la sauvegarde et le téléchargement.
          </Text>

          <Group>
            <Button
              leftSection={<IconMicrophone size={16} />}
              onClick={() => setIsRecorderOpen(true)}
              color="red"
            >
              Ouvrir l&apos;enregistreur
            </Button>
            
            <Button
              variant="outline"
              onClick={runPermissionTest}
            >
              Tester permissions
            </Button>
            
            <Button
              variant="outline"
              onClick={clearTestResults}
            >
              Effacer logs
            </Button>
          </Group>
        </Stack>
      </Card>

      {/* Composant AudioRecorder */}
      {isRecorderOpen && (
        <Card withBorder padding="lg">
          <AudioRecorder
            patientId="TEST_PATIENT_001"
            onSave={handleAudioSave}
            onCancel={handleAudioCancel}
            maxDuration={60} // 1 minute pour les tests
          />
        </Card>
      )}

      {/* Fichiers audio sauvegardés */}
      {savedAudioFiles.length > 0 && (
        <Card withBorder padding="lg">
          <Stack gap="md">
            <Group justify="space-between">
              <Text fw={600}>Fichiers audio sauvegardés</Text>
              <Badge color="green">{savedAudioFiles.length} fichier(s)</Badge>
            </Group>
            
            {savedAudioFiles.map((audioFile, index) => (
              <Card key={index} withBorder padding="sm">
                <Group justify="space-between">
                  <div>
                    <Text fw={500}>{audioFile.name}</Text>
                    <Text size="sm" c="dimmed">
                      {(audioFile.size / 1024).toFixed(2)} KB • {audioFile.type} • {audioFile.createdAt.toLocaleTimeString()}
                    </Text>
                  </div>
                  
                  <Group gap="xs">
                    <Button
                      size="xs"
                      variant="light"
                      onClick={() => downloadAudioFile(audioFile)}
                    >
                      Télécharger
                    </Button>
                    <Button
                      size="xs"
                      variant="light"
                      color="red"
                      onClick={() => deleteAudioFile(index)}
                    >
                      Supprimer
                    </Button>
                  </Group>
                </Group>
                
                {/* Lecteur audio */}
                <audio
                  controls
                  src={audioFile.url}
                  style={{ width: '100%', marginTop: '8px' }}
                />
              </Card>
            ))}
          </Stack>
        </Card>
      )}

      {/* Logs de test */}
      {testResults.length > 0 && (
        <Card withBorder padding="lg">
          <Stack gap="sm">
            <Group justify="space-between">
              <Text fw={600}>Logs de test</Text>
              <Badge color="gray">{testResults.length} entrée(s)</Badge>
            </Group>
            
            <div style={{ 
              maxHeight: '200px', 
              overflowY: 'auto',
              backgroundColor: '#f8f9fa',
              padding: '8px',
              borderRadius: '4px',
              fontFamily: 'monospace',
              fontSize: '12px'
            }}>
              {testResults.map((result, index) => (
                <div key={index} style={{ marginBottom: '4px' }}>
                  {result}
                </div>
              ))}
            </div>
          </Stack>
        </Card>
      )}

      {/* Instructions */}
      <Alert icon={<IconAlertCircle size={16} />} color="blue" variant="light">
        <Text fw={500} mb="xs">Instructions de test :</Text>
        <Text size="sm">
          1. Cliquez sur &quot;Tester permissions&quot; pour vérifier l&apos;accès au microphone<br/>
          2. Cliquez sur &quot;Ouvrir l&apos;enregistreur&quot; pour commencer un test<br/>
          3. Enregistrez un court audio et sauvegardez-le<br/>
          4. Vérifiez la lecture, le téléchargement et la suppression<br/>
          5. Consultez les logs pour voir les détails techniques
        </Text>
      </Alert>
    </Stack>
  );
};

export default AudioRecorderTest;
