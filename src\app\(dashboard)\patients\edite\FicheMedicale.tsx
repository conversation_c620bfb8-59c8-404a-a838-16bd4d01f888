'use client';
import React, { useState, useEffect } from 'react';
import { useDisclosure } from '@mantine/hooks';
import { Group, Select,Text ,Textarea,Card,Tree,TextInput,ActionIcon,Modal,Button} from '@mantine/core';
import { Checkbox,  RenderTreeNodePayload,  } from '@mantine/core';
import Icon from '@mdi/react';

// Types pour la reconnaissance vocale
interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

interface SpeechRecognitionResult {
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
}

interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionEvent {
  results: SpeechRecognitionResultList;
}

interface SpeechRecognitionErrorEvent {
  error: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onstart: (() => void) | null;
  onresult: ((event: SpeechRecognitionEvent) => void) | null;
  onerror: ((event: SpeechRecognitionErrorEvent) => void) | null;
  onend: (() => void) | null;
  start(): void;
  stop(): void;
}

interface SpeechRecognitionConstructor {
  new (): SpeechRecognition;
}

declare global {
  interface Window {
    webkitSpeechRecognition: SpeechRecognitionConstructor;
    SpeechRecognition: SpeechRecognitionConstructor;
  }
}
import { mdiViewGrid ,mdiArrowLeft,mdiArrowRight,mdiPlusBox,mdiHistory,mdiMicrophone,mdiClipboardText,mdiDeleteSweep,mdiMagnify,mdiViewHeadline,
mdiArrowRightBoldBox,mdiPlaylistCheck,mdiTagPlus,mdiChevronDown,mdiChevronRight
} from '@mdi/js';
import { TreeNodeData } from '@mantine/core';
import { IconChevronDown, IconSearch } from '@tabler/icons-react';
const renderTreeNode = ({
  node,
  expanded,
  hasChildren,
  elementProps,
  tree,
}: RenderTreeNodePayload) => {
  const checked = tree.isNodeChecked(node.value);
  const indeterminate = tree.isNodeIndeterminate(node.value);

  return (
    <Group gap="xs" {...elementProps}>
      <Checkbox.Indicator
        checked={checked}
        indeterminate={indeterminate}
        onClick={() => (!checked ? tree.checkNode(node.value) : tree.uncheckNode(node.value))}
      />

      <Group gap={5} onClick={() => tree.toggleExpanded(node.value)}>
        <span>{node.label}</span>

        {hasChildren && (
          <IconChevronDown
            size={14}
            style={{ transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)' }}
          />
        )}
      </Group>
    </Group>
  );
};
export const dataFacteurs: TreeNodeData[] = [
  {
    label: 'Facteurs de risque',
    value: 'src',
    children: [
          { label: 'Age', value: 'Age' },
          { label: 'Alcool', value: 'Alcool' },
          { label: 'Avoir une alimentation riche en sucres', value: 'Avoir une alimentation riche en sucres' },
          { label: 'Avoir une mauvaise hygiène bucco-dentaire', value: 'Avoir une mauvaise hygiène bucco-dentaire' },
          { label: 'Diabète', value: 'Diabète' },
          { label: "Etre en perte d'\autonomie", value: "Etre en perte d'\autonomie" },
          { label: 'La génétique', value: 'La génétique' },
          { label: 'La position des dents', value: 'La position des dents' },
          { label: 'Le grincement des dents (Bruxine)', value: 'Le grincement des dents (Bruxine)' },

          { label: 'Le syndrome de sjogrem', value: 'Le syndrome de sjogrem' },
          { label: 'Les traitements radiothérapie', value: 'Les traitements radiothérapie' },
           { label: 'L’HTA (hypertension artérielle)', value: 'L’HTA (hypertension artérielle)' },
            { label: 'Prise de certains médicaments:', value: 'Prise de certains médicaments:' },
             { label: 'Souffrir de sécheresse de la bouche', value: 'Souffrir de sécheresse de la bouche' },
              { label: 'Stress', value: 'Stress' },
               { label: 'Tabac', value: 'Tabac' },
                { label: 'Tabagisme', value: 'Tabagisme' },
               
    ],
  },
];
export const dataAllergies: TreeNodeData[] = [
  {
    label: 'Allergies médicamenteuses',
    value: 'Allergies médicamenteuses',
    children: [
      {
        label: 'Antalgiques',
        value: 'Antalgiques',
        children: [
          { label: 'Anesthésiques locaux', value: 'Anesthésiques locaux' },
          { label: 'Aspirine', value: 'Aspirine' },
          
        ],
      },
    ],
  },
  {
    label: 'Antibiotiques',
    value: 'Antibiotiques',
    children: [
     
          { label: 'Céphalosporines', value: 'Céphalosporines' },
          { label: 'Cyclines', value: 'Cyclines' },
           { label: 'Pénicillines et dérivés', value: 'Pénicillines et dérivés' },
          { label: 'Sulfamides', value: 'Sulfamides' },
       
     
     
    ],
  },
  {
    label: 'package.json',
    value: 'package.json',
  },
  {
    label: 'tsconfig.json',
    value: 'tsconfig.json',
  },
];
export const dataAntecedents: TreeNodeData[] = [
  {
    label: 'Allergies médicamenteuses',
    value: 'Allergies médicamenteuses',
    children: [
      {
        label: 'Antalgiques',
        value: 'Antalgiques',
        children: [
          { label: 'Anesthésiques locaux', value: 'Anesthésiques locaux' },
          { label: 'Aspirine', value: 'Aspirine' },
          
        ],
      },
    ],
  },
  {
    label: 'Antibiotiques',
    value: 'Antibiotiques',
    children: [
     
          { label: 'Céphalosporines', value: 'Céphalosporines' },
          { label: 'Cyclines', value: 'Cyclines' },
           { label: 'Pénicillines et dérivés', value: 'Pénicillines et dérivés' },
          { label: 'Sulfamides', value: 'Sulfamides' },
       
     
     
    ],
  },
  {
    label: 'package.json',
    value: 'package.json',
  },
  {
    label: 'tsconfig.json',
    value: 'tsconfig.json',
  },
];

export const FicheMedicale = () => {

    // Fonction pour obtenir la date d'aujourd'hui au format DD/MM/YYYY
    const getTodayDate = () => {
      const today = new Date();
      const day = String(today.getDate()).padStart(2, '0');
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const year = today.getFullYear();
      return `${day}/${month}/${year}`;
    };

    const [isFacteursVisible, setIsFacteursVisible] = useState(false); // State to control sidebar visibility
   const toggleFacteursSidebar = () => {
         setIsFacteursVisible(!isFacteursVisible);
       };
  const [opened, { open, close }] = useDisclosure(false);
  const [isAllergiesVisible, setIsAllergiesVisible] = useState(false); // State to control sidebar visibility
  const toggleAllergiesSidebar = () => {
         setIsAllergiesVisible(!isAllergiesVisible);
       };
  const [openedAllergies, { open:Allergiesopen, close:Allergiesclose }] = useDisclosure(false);

const [isAntecedentsVisible, setIsAntecedentsVisible] = useState(false); // State to control sidebar visibility
  const toggleAntecedentsSidebar = () => {
         setIsAntecedentsVisible(!isAntecedentsVisible);
       };
  const [openedAntecedents, { open:Antecedentsopen, close:Antecedentsclose }] = useDisclosure(false);
  
const [isTraitementVisible, setIsTraitementVisible] = useState(false); // State to control sidebar visibility
  const toggleTraitementSidebar = () => {
         setIsTraitementVisible(!isTraitementVisible);
       };
 


  
  
  

   // État pour l'expansion des facteurs de risque
    const [facteursExpanded, setFacteursExpanded] = useState(true);
    // État pour gérer les facteurs sélectionnés (multiple)
    const [selectedFacteurs, setSelectedFacteurs] = useState<string[]>([]);
    // États pour les modals
    const [historyModalOpened, setHistoryModalOpened] = useState(false);
    const [microphoneModalOpened, setMicrophoneModalOpened] = useState(false);
    const [modelsModalOpened, setModelsModalOpened] = useState(false);
    // État pour le contenu du textarea
    const [textareaContent, setTextareaContent] = useState('');
    // État pour la date sélectionnée
    const [selectedDate, setSelectedDate] = useState<string>(getTodayDate());
    // État pour l'historique des sélections
    const [historyEntries, setHistoryEntries] = useState<Array<{date: string, content: string}>>([
      { date: '26/06/2025', content: 'Age , La position des dents , Tabac' },
      { date: '27/06/2025', content: 'Alcool , Diabète , La génétique' }
    ]);

    // État pour gérer les données des facteurs (copie modifiable)
    const [currentDataFacteurs, setCurrentDataFacteurs] = useState<TreeNodeData[]>(dataFacteurs);

    // États pour la reconnaissance vocale
    const [isListening, setIsListening] = useState(false);
    const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);

    // États pour la gestion des modèles
    const [models, setModels] = useState<Array<{id: string, name: string, content: string}>>([]);
    const [showAddModel, setShowAddModel] = useState(false);
    const [newModelName, setNewModelName] = useState('');
    const [newModelContent, setNewModelContent] = useState('');

    // Fonction pour gérer la sélection/déselection des facteurs
    const handleFacteurSelection = (facteur: string) => {
      setSelectedFacteurs(prev => {
        const isSelected = prev.includes(facteur);
        let newSelection;

        if (isSelected) {
          // Retirer le facteur s'il est déjà sélectionné
          newSelection = prev.filter(f => f !== facteur);
        } else {
          // Ajouter le facteur s'il n'est pas sélectionné
          newSelection = [...prev, facteur];
        }

        // Mettre à jour le contenu du textarea
        setTextareaContent(newSelection.join(' , '));
        return newSelection;
      });
    };

    // Fonction pour sauvegarder les sélections avec la date
    const saveSelectionToHistory = (contentToSave?: string) => {
      const content = contentToSave || textareaContent;
      if (content.trim()) {
        const newEntry = {
          date: selectedDate,
          content: content
        };
        setHistoryEntries(prev => [newEntry, ...prev]);
      }
    };

    // Fonction pour ajouter un nouveau facteur à la liste
    const addNewFacteur = () => {
      if (textareaContent.trim()) {
        const newFacteur = {
          label: textareaContent.trim(),
          value: textareaContent.trim()
        };

        setCurrentDataFacteurs(prev => {
          const newData = [...prev];
          if (newData[0] && newData[0].children) {
            newData[0].children = [...newData[0].children, newFacteur];
          }
          return newData;
        });

        // Sauvegarder dans l'historique
        saveSelectionToHistory();

        // Ne pas vider le textarea - garder le contenu
        // setTextareaContent('');
      }
    };

    // Fonction pour initialiser la reconnaissance vocale
    const initSpeechRecognition = () => {
      try {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
          const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;
          const recognition = new SpeechRecognition();

          recognition.continuous = false;
          recognition.interimResults = false;
          recognition.lang = 'fr-FR';

          recognition.onstart = () => {
            console.log('Reconnaissance vocale démarrée');
            setIsListening(true);
          };

          recognition.onresult = (event: SpeechRecognitionEvent) => {
            const transcript = event.results[0][0].transcript;
            console.log('Texte reconnu:', transcript);
            setTextareaContent(prev => prev + ' ' + transcript);
          };

          recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
            console.error('Erreur de reconnaissance vocale:', event.error);
            setIsListening(false);
          };

          recognition.onend = () => {
            console.log('Reconnaissance vocale terminée');
            setIsListening(false);
          };

          setRecognition(recognition);
          console.log('Reconnaissance vocale initialisée');
        } else {
          console.warn('API de reconnaissance vocale non supportée par ce navigateur');
        }
      } catch (error) {
        console.error('Erreur lors de l\'initialisation de la reconnaissance vocale:', error);
      }
    };

    // Fonction pour démarrer/arrêter la reconnaissance vocale
    const toggleSpeechRecognition = () => {
      console.log('toggleSpeechRecognition appelée, recognition:', !!recognition, 'isListening:', isListening);

      if (!recognition) {
        console.log('Initialisation de la reconnaissance vocale...');
        initSpeechRecognition();
        return;
      }

      try {
        if (isListening) {
          console.log('Arrêt de la reconnaissance vocale');
          recognition.stop();
        } else {
          console.log('Démarrage de la reconnaissance vocale');
          recognition.start();
        }
      } catch (error) {
        console.error('Erreur lors du toggle:', error);
      }
    };

    // Fonction pour ajouter un nouveau modèle
    const addNewModel = () => {
      if (newModelName.trim() && newModelContent.trim()) {
        const newModel = {
          id: Date.now().toString(),
          name: newModelName.trim(),
          content: newModelContent.trim()
        };

        setModels(prev => [...prev, newModel]);
        setNewModelName('');
        setNewModelContent('');
        setShowAddModel(false);
      }
    };

    // Fonction pour appliquer un modèle
    const applyModel = (modelContent: string) => {
      setTextareaContent(modelContent);
      setModelsModalOpened(false);
    };

    // Effet pour initialiser la reconnaissance vocale
    useEffect(() => {
      initSpeechRecognition();
    }, []);
  return (
    <>
    <Group justify="space-between" mb={10}>
      <Group>
       <Icon path={mdiViewGrid} size={1} />
         <Text fw={700}>Fiche médicale</Text>
      </Group>
      <Group justify="flex-end">
      <Icon path={mdiArrowLeft} size={1} />
     <Select
      //placeholder="getTodayDate"
      data={[ getTodayDate()]}
      value={selectedDate}
      onChange={(value) => setSelectedDate(value || getTodayDate())}
    />
      <Icon path={mdiArrowRight} size={1} />
    </Group>
    </Group>
    <Group>
<div className={isFacteursVisible || isAllergiesVisible || isAntecedentsVisible  ? 'w-[79%] ':'w-[100%] '}>
    {/* --------------------------1-------------------------  */}
 <Group  w={"100%"}>
  <div className={ isFacteursVisible ? 'w-[49%]':'w-[49%]'}>
  <Group  justify="space-between" gap="sm"  > 
    <Group gap="sm">
  <Text fw={500}>Facteurs de risque</Text>
    </Group>
   <Group justify="flex-end" gap="sm">
        {textareaContent.trim() && (
          <ActionIcon variant="transparent" onClick={addNewFacteur} title="Ajouter à la liste">
            <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
          </ActionIcon>
        )}
        <ActionIcon variant="transparent" onClick={() => setHistoryModalOpened(true)}>
          <Icon path={mdiHistory} size={1} color={"#3799ce"}/>
        </ActionIcon>
        <ActionIcon variant="transparent" onClick={() => setMicrophoneModalOpened(true)}>
          <Icon path={mdiMicrophone} size={1} color={"#3799ce"}/>
        </ActionIcon>
        <ActionIcon variant="transparent" onClick={() => setModelsModalOpened(true)}>
          <Icon path={mdiClipboardText} size={1} color={"#3799ce"}/>
        </ActionIcon>
        <ActionIcon variant="transparent" onClick={() => {
          setTextareaContent('');
          setSelectedFacteurs([]);
        }}>
          <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
        </ActionIcon>
      </Group>
   </Group>
    <Textarea
      size="xl"
      radius="md"
      placeholder="Ajouter"
      mt={10}
      mb={10}
      value={textareaContent}
      onChange={(event) => setTextareaContent(event.currentTarget.value)}
      onClick={(event) => {
        event.preventDefault();
        toggleFacteursSidebar();
      }}
    />
</div>
     <div className={isAllergiesVisible ? 'w-[49%]':'w-[49%]'}>
  <Group  justify="space-between" gap="sm"  > 
    <Group gap="sm">
  <Text fw={500}>Allergies médicamenteuses
</Text>
    </Group>
    <Group justify="flex-end" gap="sm">
        <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
        <Icon path={mdiHistory} size={1} color={"#3799ce"}/>
        <Icon path={mdiMicrophone} size={1} color={"#3799ce"}/>
        <Icon path={mdiClipboardText} size={1} color={"#3799ce"}/>
        <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
    </Group>
   </Group>
    <Textarea
      size="xl"
      radius="md"
      placeholder="Ajouter"
      mt={10}
      mb={10}
      value={''}
      onChange={() => {}}
       onClick={(event) => {
                event.preventDefault();
                toggleAllergiesSidebar();
       }}
    />
    
</div>
 </Group>
 {/* ---------------------------2----------------------- */}
  <Group  w={"100%"}>
  <div className={isAntecedentsVisible ? 'w-[49%]':'w-[49%]'}>
  <Group  justify="space-between" gap="sm"  > 
    <Group gap="sm">
  <Text fw={500}>Antécédents</Text>
    </Group>
    <Group justify="flex-end" gap="sm">
        <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
        <Icon path={mdiHistory} size={1} color={"#3799ce"}/>
        <Icon path={mdiMicrophone} size={1} color={"#3799ce"}/>
        <Icon path={mdiClipboardText} size={1} color={"#3799ce"}/>
        <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
    </Group>
   </Group>
    <Textarea
      size="xl"
      radius="md"
      placeholder="Ajouter"
      mt={10}
      mb={10}
       onClick={(event) => {
                event.preventDefault();
                toggleAntecedentsSidebar(); // Toggle sidebar visibility
              }}
    />
</div>
     <div className={isTraitementVisible ? 'w-[49%]':'w-[49%]'}>
  <Group  justify="space-between" gap="sm"  > 
    
    <Group gap="sm">
  <Text fw={500}>Traitement en cours

</Text>
    </Group>
    <Group justify="flex-end" gap="sm">
        <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
        <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
    </Group>
   </Group>
    <Textarea
      size="xl"
      radius="md"
      placeholder="Saisir"
      mt={10}
      mb={10}
       onClick={(event) => {
                event.preventDefault();
                toggleTraitementSidebar(); 
              }}
    />
    
</div>
 </Group>
 {/* ---------------------------3--------------------------- */}
  <Group  w={"100%"}>
  <div className='w-[49%]'>
  <Group  justify="space-between" gap="sm"  > 
    <Group gap="sm">
  <Text fw={500}>Pathologies
</Text>
    </Group>
    <Group justify="flex-end" gap="sm">
        <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
        <Icon path={mdiTagPlus} size={1} color={"#3799ce"}/>
        <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
    </Group>
   </Group>
    <Textarea
      size="xl"
      radius="md"
      placeholder="Saisir"
      mt={10}
      mb={10}
      
    />
</div>
     <div className={isAllergiesVisible ? 'w-[49%]':'w-[49%]'}>
  
    
</div>
 </Group>
  </div>
   {/* --------------------------1-------------------------  */}
    {isFacteursVisible &&(
    <div className='w-[20%]' style={{ alignSelf: 'flex-start' }}>
        <Group justify="space-between">
           <TextInput
           
              placeholder="Rechercher"
              leftSection={<IconSearch size={16} />}
              style={{ flex: 1 }}
           
            />
        <Group gap={"8px"}>
           <ActionIcon variant="filled"   onClick={open}>
             <Icon path={mdiViewHeadline} size={1} />
          </ActionIcon>
             <ActionIcon variant="filled" aria-label="Settings"  onClick={() => toggleFacteursSidebar()}>
     <Icon path={mdiArrowRightBoldBox} size={1}    />
       </ActionIcon>
      </Group>
      </Group>
        <div style={{ backgroundColor: '#f9f9f9' }} className='p-4'>
          <div>
            <Group mb="xs" style={{ cursor: 'pointer' }} onClick={() => setFacteursExpanded(!facteursExpanded)}>
              <Icon
                path={facteursExpanded ? mdiChevronDown : mdiChevronRight}
                size={0.8}
              />
              <Text fw={500} size="sm">Facteurs de risque</Text>
            </Group>
            {facteursExpanded && (
              <div style={{ marginLeft: '20px' }}>
             {currentDataFacteurs[0]?.children?.map((facteur, index) => (
                  <div key={index} style={{ marginBottom: '4px' }}>
                    <Text
                      size="sm"
                      style={{
                        color: selectedFacteurs.includes(String(facteur.label)) ? '#3799ce' : '#666',
                        cursor: 'pointer',
                        padding: "1px 2px 1px 4px",
                        backgroundColor: selectedFacteurs.includes(String(facteur.label)) ? '#e3f2fd' : 'transparent',
                        borderRadius: '4px'
                      }}
                      onClick={() => handleFacteurSelection(String(facteur.label))}
                      className='dataFacteursSelect'
                    >
                      {facteur.label}
                    </Text>

                  </div>
                ))}
         </div>
                )}
          </div>
        </div> 
 </div>
    )}
    
     { isAllergiesVisible &&(
    <div className='w-[20%]'>
        <Group justify="space-between">
        <TextInput
        placeholder="Custom layout"
        leftSection={<Icon path={mdiMagnify} size={1} />}
       
      />
        <Group>
            <ActionIcon variant="filled" aria-label="Settings"  onClick={Allergiesopen}>
      <Icon path={mdiViewHeadline} size={1} />
       </ActionIcon>
       <ActionIcon variant="filled" aria-label="Settings"  onClick={() => toggleAllergiesSidebar()}>
     <Icon path={mdiArrowRightBoldBox} size={1}     />
       </ActionIcon>
      </Group>
      </Group>
      <Card shadow="sm" padding="lg" radius="md" withBorder >
    <Tree data={dataAllergies} />
      </Card>
 </div>
    )}
     {/* ---------------------------2----------------------- */}
      {isAntecedentsVisible &&(
    <div className='w-[20%]'>
        <Group justify="space-between">
        <TextInput
        placeholder="Custom layout"
        leftSection={<Icon path={mdiMagnify} size={1} />}
       
      />
        <Group>
            <ActionIcon variant="filled" aria-label="Settings"  onClick={Antecedentsopen}>
      <Icon path={mdiViewHeadline} size={1} />
       </ActionIcon>
       <ActionIcon variant="filled" aria-label="Settings"  onClick={() => toggleAntecedentsSidebar()}>
     <Icon path={mdiArrowRightBoldBox} size={1}     />
       </ActionIcon>
      </Group>
      </Group>
      <Card shadow="sm" padding="lg" radius="md" withBorder >
    <Tree data={dataAntecedents} />
      </Card>
 </div>
    )}
    
   
     {/* ---------------------------3--------------------------- */}
   
    
   
    </Group>

     <Modal opened={opened} onClose={close} title={<><Icon path={mdiViewHeadline} size={1} color="#3799ce" /> Choix multiple</>} centered size="md">
       <div style={{ padding: '10px' }}>
         <Group mb="md">
           <Icon path={mdiChevronDown} size={0.8} />
           <Checkbox
             checked={selectedFacteurs.length === currentDataFacteurs[0]?.children?.length}
             indeterminate={selectedFacteurs.length > 0 && selectedFacteurs.length < (currentDataFacteurs[0]?.children?.length || 0)}
             onChange={(event) => {
               if (event.currentTarget.checked) {
                 // Sélectionner tous
                 const allFacteurs = currentDataFacteurs[0]?.children?.map(f => String(f.label)) || [];
                 setSelectedFacteurs(allFacteurs);
                 setTextareaContent(allFacteurs.join(' , '));
               } else {
                 // Désélectionner tous
                 setSelectedFacteurs([]);
                 setTextareaContent('');
               }
             }}
           />
           <Text fw={500}>Facteurs de risque</Text>
         </Group>
         <div style={{ marginLeft: '20px' }}>
           {currentDataFacteurs[0]?.children?.map((facteur, index) => (
             <Group key={index} mb="xs" style={{ marginLeft: '20px' }}>
               <Checkbox
                 checked={selectedFacteurs.includes(String(facteur.label))}
                 onChange={() => handleFacteurSelection(String(facteur.label))}
               />
               <Text size="sm">{facteur.label}</Text>
             </Group>
           ))}
         </div>
         <Group justify="flex-end" mt="md">
           <Button color="blue" onClick={() => {
             // Mettre à jour le textarea avec les sélections
             const selectedText = selectedFacteurs.join(' , ');
             setTextareaContent(selectedText);

             // Sauvegarder dans l'historique si il y a des sélections
             if (selectedFacteurs.length > 0) {
               saveSelectionToHistory(selectedText);
             }

             close();
           }}>Valider</Button>
           <Button variant="outline" color="red" onClick={close}>Annuler</Button>
         </Group>
       </div>
      </Modal>
       <Modal opened={openedAllergies} onClose={Allergiesclose} title={<> <Group><Icon path={mdiPlaylistCheck} size={1} /> Choix multiple</Group></>} centered>
       <Tree data={dataAllergies} levelOffset={23} expandOnClick={false} renderNode={renderTreeNode} />
      </Modal>
 {/* ---------------------------2----------------------- */}
  <Modal opened={openedAntecedents} onClose={Antecedentsclose} title="Authentication" centered>
       <Icon path={mdiPlaylistCheck} size={1} /> Choix multiple
      </Modal>
     
       {/* --------------------------3----------------------- */}
        
      


      {/* Modal Historique */}
            <Modal
              opened={historyModalOpened}
              onClose={() => setHistoryModalOpened(false)}
              title={
                <Group>
                  <Icon path={mdiHistory} size={1} color="#3799ce" />
                  <Text>Dictionnaire : Historique du champ</Text>
                </Group>
              }
              centered
              size="md"
            >
              <div>
                {historyEntries.map((entry, index) => (
                  <Group key={index} mb="md" style={{ backgroundColor: '#e3f2fd', padding: '8px', borderRadius: '4px' }}>
                    <Text size="sm" fw={500} c="blue">{entry.date}</Text>
                    <Text size="sm">{entry.content}</Text>
                    <Group ml="auto">
                      <ActionIcon variant="transparent" size="sm" onClick={() => setTextareaContent(entry.content)}>
                        <Icon path={mdiPlusBox} size={0.8} color="#3799ce" />
                      </ActionIcon>
                      <ActionIcon variant="transparent" size="sm">
                        <Icon path={mdiHistory} size={0.8} color="#3799ce" />
                      </ActionIcon>
                    </Group>
                  </Group>
                ))}
              </div>
            </Modal>
      
            {/* Modal Microphone */}
            <Modal
              opened={microphoneModalOpened}
              onClose={() => setMicrophoneModalOpened(false)}
              title=""
              centered
              size="lg"
              withCloseButton={false}
            >
              <div style={{ padding: '20px' }}>
                <Textarea
                  placeholder="ag , La position des dents , Tabac"
                  minRows={4}
                  value={textareaContent}
                  onChange={(event) => setTextareaContent(event.currentTarget.value)}
                  rightSection={
                    <Group>
                      <ActionIcon
                        variant="transparent"
                        onClick={toggleSpeechRecognition}
                        style={{ backgroundColor: isListening ? '#ffecb3' : 'transparent' }}
                      >
                        <Icon path={mdiMicrophone} size={1} color={isListening ? "#f57c00" : "#ff9800"} />
                      </ActionIcon>
                      <ActionIcon variant="transparent" onClick={() => setMicrophoneModalOpened(false)}>
                        <Text size="lg">×</Text>
                      </ActionIcon>
                    </Group>
                  }
                />
                <Group justify="flex-end" mt="md">
                  <Button variant="filled" color="blue" onClick={() => setMicrophoneModalOpened(false)}>
                    Valider
                  </Button>
                  <Button variant="outline" color="red" onClick={() => setMicrophoneModalOpened(false)}>
                    Annuler
                  </Button>
                </Group>
              </div>
            </Modal>
      
            {/* Modal Modèles */}
            <Modal
              opened={modelsModalOpened}
              onClose={() => setModelsModalOpened(false)}
              title={
                <Group>
                  <Icon path={mdiClipboardText} size={1} color="#3799ce" />
                  <Text>Modèles de dictionnaire</Text>
                  <Group ml="auto">
                    <ActionIcon variant="transparent" onClick={() => setShowAddModel(true)}>
                      <Icon path={mdiPlusBox} size={1} color="#3799ce" />
                    </ActionIcon>
                  </Group>
                </Group>
              }
              centered
              size="md"
            >
              {/* Formulaire d'ajout de modèle */}
              {showAddModel && (
                <div style={{ backgroundColor: '#f5f5f5', padding: '16px', borderRadius: '4px', marginBottom: '16px' }}>
                  <Text size="sm" fw={500} mb="xs">Ajouter un nouveau modèle</Text>
                  <TextInput
                    placeholder="Nom du modèle"
                    value={newModelName}
                    onChange={(event) => setNewModelName(event.currentTarget.value)}
                    mb="xs"
                  />
                  <Textarea
                    placeholder="Contenu du modèle"
                    value={newModelContent}
                    onChange={(event) => setNewModelContent(event.currentTarget.value)}
                    minRows={3}
                    mb="xs"
                  />
                  <Group justify="flex-end">
                    <Button size="xs" variant="outline" onClick={() => setShowAddModel(false)}>
                      Annuler
                    </Button>
                    <Button size="xs" onClick={addNewModel}>
                      Enregistrer
                    </Button>
                  </Group>
                </div>
              )}

              {/* Liste des modèles */}
              {models.length === 0 ? (
                <div style={{ backgroundColor: '#fff3cd', padding: '16px', borderRadius: '4px', marginBottom: '16px' }}>
                  <Group>
                    <Icon path={mdiHistory} size={1} color="#ff9800" />
                    <Text size="sm" c="#856404">Aucun modèle à afficher</Text>
                  </Group>
                </div>
              ) : (
                <div style={{ marginBottom: '16px' }}>
                  {models.map((model) => (
                    <div key={model.id} style={{ backgroundColor: '#e3f2fd', padding: '12px', borderRadius: '4px', marginBottom: '8px' }}>
                      <Group justify="space-between">
                        <div>
                          <Text size="sm" fw={500} c="blue">{model.name}</Text>
                          <Text size="xs" c="dimmed">{model.content}</Text>
                        </div>
                        <ActionIcon variant="transparent" onClick={() => applyModel(model.content)}>
                          <Icon path={mdiPlusBox} size={0.8} color="#3799ce" />
                        </ActionIcon>
                      </Group>
                    </div>
                  ))}
                </div>
              )}

              <Group justify="flex-end">
                <Button variant="filled" color="blue" onClick={() => setModelsModalOpened(false)}>
                  Valider
                </Button>
                <Button variant="outline" color="red" onClick={() => setModelsModalOpened(false)}>
                  Annuler
                </Button>
              </Group>
            </Modal>
    </>  
  )
}
