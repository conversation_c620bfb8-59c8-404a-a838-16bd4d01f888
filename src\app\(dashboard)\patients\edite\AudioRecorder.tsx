'use client';
import React, { useState, useRef, useEffect } from 'react';
import {
  Button,
  Group,
  Text,
  Progress,
  Alert,
  Stack,
  ActionIcon,
  Card,
  Badge,
  Tooltip,
} from '@mantine/core';
import {
  IconMicrophone,
  IconPlayerStop,
  IconPlayerPlay,
  IconPlayerPause,
  IconTrash,
  IconDownload,
  IconAlertCircle,
  IconCheck,
} from '@tabler/icons-react';

interface AudioRecorderProps {
  onSave?: (audioBlob: Blob, fileName: string) => void;
  onCancel?: () => void;
  maxDuration?: number; // en secondes
  patientId?: string;
}

export const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onSave,
  onCancel,
  maxDuration = 300, // 5 minutes par défaut
  patientId
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [permission, setPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const chunksRef = useRef<Blob[]>([]);

  // Vérifier les permissions au montage
  useEffect(() => {
    checkPermissions();
    return () => {
      cleanup();
    };
  }, []);

  // Timer pour l'enregistrement
  useEffect(() => {
    if (isRecording && !isPaused) {
      intervalRef.current = setInterval(() => {
        setRecordingTime(prev => {
          if (prev >= maxDuration) {
            stopRecording();
            return prev;
          }
          return prev + 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRecording, isPaused, maxDuration]);

  const checkPermissions = async () => {
    try {
      const result = await navigator.permissions.query({ name: 'microphone' as PermissionName });
      setPermission(result.state);
      
      result.addEventListener('change', () => {
        setPermission(result.state);
      });
    } catch (err) {
      console.warn('Permission API not supported');
    }
  };

  const cleanup = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
  };

  const startRecording = async () => {
    try {
      setError(null);
      
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        } 
      });
      
      streamRef.current = stream;
      chunksRef.current = [];

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'audio/webm;codecs=opus' });
        setAudioBlob(blob);
        
        if (audioUrl) {
          URL.revokeObjectURL(audioUrl);
        }
        
        const url = URL.createObjectURL(blob);
        setAudioUrl(url);
        
        cleanup();
      };

      mediaRecorder.start(1000); // Collecte des données toutes les secondes
      setIsRecording(true);
      setIsPaused(false);
      setRecordingTime(0);
      
    } catch (err) {
      console.error('Erreur lors du démarrage de l\'enregistrement:', err);
      setError('Impossible d\'accéder au microphone. Vérifiez les permissions.');
    }
  };

  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.pause();
      setIsPaused(true);
    }
  };

  const resumeRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.resume();
      setIsPaused(false);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setIsPaused(false);
    }
  };

  const playAudio = () => {
    if (audioRef.current && audioUrl) {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const pauseAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const deleteRecording = () => {
    setAudioBlob(null);
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      setAudioUrl(null);
    }
    setRecordingTime(0);
    setIsPlaying(false);
  };

  const saveRecording = () => {
    if (audioBlob && onSave) {
      const fileName = `audio_${patientId || 'patient'}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
      onSave(audioBlob, fileName);
    }
  };

  const downloadRecording = () => {
    if (audioBlob && audioUrl) {
      const fileName = `audio_${patientId || 'patient'}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
      const a = document.createElement('a');
      a.href = audioUrl;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const progressPercentage = (recordingTime / maxDuration) * 100;

  return (
    <Stack gap="md">
      {error && (
        <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
          {error}
        </Alert>
      )}

      {permission === 'denied' && (
        <Alert icon={<IconAlertCircle size={16} />} color="orange" variant="light">
          L&apos;accès au microphone est refusé. Veuillez autoriser l&apos;accès dans les paramètres de votre navigateur.
        </Alert>
      )}

      {/* Zone d'enregistrement */}
      <Card withBorder padding="lg" style={{ textAlign: 'center' }}>
        <Stack gap="md">
          {/* Indicateur visuel */}
          <div style={{ position: 'relative', display: 'inline-block' }}>
            <IconMicrophone 
              size={64} 
              color={isRecording ? (isPaused ? '#ffa500' : '#e53935') : '#868e96'} 
            />
            {isRecording && (
              <Badge
                color={isPaused ? 'orange' : 'red'}
                variant="filled"
                style={{
                  position: 'absolute',
                  top: -5,
                  right: -5,
                  animation: isPaused ? 'none' : 'pulse 1s infinite'
                }}
              >
                {isPaused ? 'PAUSE' : 'REC'}
              </Badge>
            )}
          </div>

          {/* Timer et barre de progression */}
          <div>
            <Text size="xl" fw={700} c={isRecording ? 'red' : 'dimmed'}>
              {formatTime(recordingTime)}
            </Text>
            <Text size="sm" c="dimmed">
              / {formatTime(maxDuration)}
            </Text>
            {isRecording && (
              <Progress 
                value={progressPercentage} 
                color={progressPercentage > 90 ? 'red' : 'blue'}
                mt="xs"
              />
            )}
          </div>

          {/* Contrôles d'enregistrement */}
          <Group justify="center" gap="md">
            {!isRecording && !audioBlob && (
              <Button
                leftSection={<IconMicrophone size={16} />}
                onClick={startRecording}
                color="red"
                disabled={permission === 'denied'}
              >
                Commencer l&apos;enregistrement
              </Button>
            )}

            {isRecording && (
              <>
                {!isPaused ? (
                  <Tooltip label="Mettre en pause">
                    <ActionIcon size="lg" variant="filled" color="orange" onClick={pauseRecording}>
                      <IconPlayerPause size={20} />
                    </ActionIcon>
                  </Tooltip>
                ) : (
                  <Tooltip label="Reprendre">
                    <ActionIcon size="lg" variant="filled" color="red" onClick={resumeRecording}>
                      <IconPlayerPlay size={20} />
                    </ActionIcon>
                  </Tooltip>
                )}

                <Tooltip label="Arrêter">
                  <ActionIcon size="lg" variant="filled" color="dark" onClick={stopRecording}>
                    <IconPlayerStop size={20} />
                  </ActionIcon>
                </Tooltip>
              </>
            )}
          </Group>
        </Stack>
      </Card>

      {/* Lecture de l'enregistrement */}
      {audioBlob && audioUrl && (
        <Card withBorder padding="md">
          <Stack gap="sm">
            <Group justify="space-between">
              <Text fw={500}>Enregistrement terminé</Text>
              <Badge color="green" leftSection={<IconCheck size={12} />}>
                {formatTime(recordingTime)}
              </Badge>
            </Group>

            <audio
              ref={audioRef}
              src={audioUrl}
              onEnded={() => setIsPlaying(false)}
              style={{ width: '100%' }}
              controls
            />

            <Group justify="center" gap="xs">
              <Tooltip label="Télécharger">
                <ActionIcon variant="light" color="blue" onClick={downloadRecording}>
                  <IconDownload size={16} />
                </ActionIcon>
              </Tooltip>

              <Tooltip label="Supprimer">
                <ActionIcon variant="light" color="red" onClick={deleteRecording}>
                  <IconTrash size={16} />
                </ActionIcon>
              </Tooltip>
            </Group>
          </Stack>
        </Card>
      )}

      {/* Boutons d'action */}
      <Group justify="flex-end" mt="md">
        <Button variant="outline" onClick={onCancel}>
          Annuler
        </Button>
        
        {audioBlob && (
          <Button 
            leftSection={<IconCheck size={16} />}
            onClick={saveRecording}
            color="green"
          >
            Enregistrer le fichier
          </Button>
        )}
      </Group>

      <style jsx>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `}</style>
    </Stack>
  );
};

export default AudioRecorder;
