'use client';
import React from 'react';
import { Container, Title, Text, Stack, Card, Group, Badge, Alert } from '@mantine/core';
import { IconMicrophone, IconInfoCircle } from '@tabler/icons-react';
import AudioRecorderTest from './AudioRecorderTest';

export default function DemoAudioPage() {
  return (
    <Container size="lg" py="xl">
      <Stack gap="xl">
        {/* En-tête */}
        <Card withBorder padding="xl" style={{ textAlign: 'center' }}>
          <Group justify="center" mb="md">
            <IconMicrophone size={48} color="#e53935" />
          </Group>
          
          <Title order={1} mb="md">
            Démonstration AudioRecorder
          </Title>
          
          <Text size="lg" c="dimmed" mb="md">
            Composant d&apos;enregistrement audio complet pour les dossiers patients
          </Text>
          
          <Group justify="center" gap="md">
            <Badge color="green" size="lg">Enregistrement</Badge>
            <Badge color="blue" size="lg">Lecture</Badge>
            <Badge color="orange" size="lg">Sauvegarde</Badge>
            <Badge color="purple" size="lg">Téléchargement</Badge>
          </Group>
        </Card>

        {/* Fonctionnalités */}
        <Card withBorder padding="lg">
          <Title order={3} mb="md">Fonctionnalités implémentées</Title>
          
          <Stack gap="sm">
            <Group>
              <Badge color="green" variant="light">✅</Badge>
              <Text>Enregistrement audio haute qualité (WebM/Opus)</Text>
            </Group>
            
            <Group>
              <Badge color="green" variant="light">✅</Badge>
              <Text>Contrôles pause/reprise pendant l&apos;enregistrement</Text>
            </Group>
            
            <Group>
              <Badge color="green" variant="light">✅</Badge>
              <Text>Timer avec limite de durée configurable</Text>
            </Group>
            
            <Group>
              <Badge color="green" variant="light">✅</Badge>
              <Text>Barre de progression visuelle</Text>
            </Group>
            
            <Group>
              <Badge color="green" variant="light">✅</Badge>
              <Text>Lecture immédiate de l&apos;enregistrement</Text>
            </Group>
            
            <Group>
              <Badge color="green" variant="light">✅</Badge>
              <Text>Sauvegarde automatique avec nom de fichier généré</Text>
            </Group>
            
            <Group>
              <Badge color="green" variant="light">✅</Badge>
              <Text>Téléchargement direct du fichier audio</Text>
            </Group>
            
            <Group>
              <Badge color="green" variant="light">✅</Badge>
              <Text>Gestion des permissions microphone</Text>
            </Group>
            
            <Group>
              <Badge color="green" variant="light">✅</Badge>
              <Text>Gestion d&apos;erreurs complète</Text>
            </Group>
            
            <Group>
              <Badge color="green" variant="light">✅</Badge>
              <Text>Interface responsive et accessible</Text>
            </Group>
          </Stack>
        </Card>

        {/* Spécifications techniques */}
        <Card withBorder padding="lg">
          <Title order={3} mb="md">Spécifications techniques</Title>
          
          <Stack gap="sm">
            <Group justify="space-between">
              <Text fw={500}>Format audio :</Text>
              <Badge variant="outline">WebM + Opus Codec</Badge>
            </Group>
            
            <Group justify="space-between">
              <Text fw={500}>Qualité :</Text>
              <Badge variant="outline">44.1 kHz, Stéréo</Badge>
            </Group>
            
            <Group justify="space-between">
              <Text fw={500}>Durée max :</Text>
              <Badge variant="outline">Configurable (défaut: 5 min)</Badge>
            </Group>
            
            <Group justify="space-between">
              <Text fw={500}>Taille fichier :</Text>
              <Badge variant="outline">~1 MB/minute</Badge>
            </Group>
            
            <Group justify="space-between">
              <Text fw={500}>Compatibilité :</Text>
              <Badge variant="outline">Chrome, Firefox, Safari, Edge</Badge>
            </Group>
            
            <Group justify="space-between">
              <Text fw={500}>Framework :</Text>
              <Badge variant="outline">React + TypeScript + Mantine</Badge>
            </Group>
          </Stack>
        </Card>

        {/* Alertes importantes */}
        <Alert icon={<IconInfoCircle size={16} />} color="blue" variant="light">
          <Text fw={500} mb="xs">Notes importantes :</Text>
          <Text size="sm">
            • L&apos;enregistrement audio nécessite une connexion HTTPS en production<br/>
            • Les permissions microphone doivent être accordées par l&apos;utilisateur<br/>
            • Les fichiers sont sauvegardés localement et peuvent être intégrés à votre système de stockage<br/>
            • Le composant est optimisé pour les dossiers médicaux avec nommage automatique par patient
          </Text>
        </Alert>

        {/* Interface de test */}
        <AudioRecorderTest />

        {/* Code d'intégration */}
        <Card withBorder padding="lg">
          <Title order={3} mb="md">Intégration dans AttachmentManager</Title>
          
          <Text mb="md" c="dimmed">
            Le composant AudioRecorder est déjà intégré dans AttachmentManager.tsx :
          </Text>
          
          <div style={{
            backgroundColor: '#f8f9fa',
            padding: '16px',
            borderRadius: '8px',
            fontFamily: 'monospace',
            fontSize: '14px',
            overflow: 'auto'
          }}>
            <pre>{`<AudioRecorder
  patientId={patientId}
  onSave={handleAudioSave}
  onCancel={() => setIsMicrophoneVisible(false)}
  maxDuration={300} // 5 minutes
/>`}</pre>
          </div>
          
          <Text mt="md" size="sm" c="dimmed">
            Pour utiliser le composant, cliquez simplement sur l&apos;icône microphone 🎤 dans la barre d&apos;outils d&apos;AttachmentManager.
          </Text>
        </Card>
      </Stack>
    </Container>
  );
}
