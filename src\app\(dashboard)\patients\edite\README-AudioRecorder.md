# AudioRecorder - Système d'enregistrement audio pour dossiers patients

## 📋 Vue d'ensemble

Le système AudioRecorder est un composant React TypeScript complet pour l'enregistrement, la lecture et la sauvegarde de fichiers audio dans les dossiers patients. Il est intégré dans AttachmentManager.tsx et offre une interface moderne et intuitive.

## 🎯 Fonctionnalités principales

### ✅ Enregistrement audio
- **Format haute qualité** : WebM avec codec Opus
- **Contrôles avancés** : Pause/Reprise pendant l'enregistrement
- **Timer visuel** : Affichage du temps écoulé et barre de progression
- **Limite de durée** : Configurable (défaut: 5 minutes)
- **Arrêt automatique** : Quand la limite est atteinte

### ✅ Interface utilisateur
- **Indicateur visuel** : Icône microphone avec badge REC/PAUSE animé
- **Feedback temps réel** : Timer et progression visuelle
- **Contrôles intuitifs** : Boutons play/pause/stop avec tooltips
- **Design responsive** : Compatible mobile et desktop

### ✅ Lecture et gestion
- **Lecture immédiate** : Player audio intégré après enregistrement
- **Téléchargement direct** : Bouton de téléchargement du fichier
- **Suppression** : Possibilité de supprimer et recommencer
- **Prévisualisation** : Contrôles audio HTML5 natifs

### ✅ Sauvegarde et intégration
- **Nommage automatique** : `audio_PATIENT_ID_YYYY-MM-DD-HH-mm-ss.webm`
- **Intégration AttachmentManager** : Ajout automatique à la liste des fichiers
- **Callback personnalisé** : Fonction onSave pour intégration système
- **Gestion des erreurs** : Alertes utilisateur en cas de problème

## 🔧 Installation et utilisation

### 1. Composants inclus

```
src/app/(dashboard)/patients/edite/
├── AudioRecorder.tsx          # Composant principal d'enregistrement
├── AttachmentManager.tsx      # Gestionnaire de pièces jointes (intégré)
├── AudioRecorderTest.tsx      # Interface de test
├── demo-audio.tsx            # Page de démonstration
└── README-AudioRecorder.md   # Cette documentation
```

### 2. Utilisation dans AttachmentManager

Le composant est déjà intégré dans AttachmentManager.tsx. Pour l'utiliser :

1. **Ouvrir AttachmentManager** dans un dossier patient
2. **Cliquer sur l'icône microphone** 🎤 dans la barre d'outils
3. **Autoriser l'accès au microphone** si demandé
4. **Enregistrer votre audio** avec les contrôles
5. **Sauvegarder** le fichier dans le dossier patient

### 3. Utilisation standalone

```typescript
import AudioRecorder from './AudioRecorder';

<AudioRecorder
  patientId="PATIENT_001"
  onSave={(audioBlob, fileName) => {
    // Gérer la sauvegarde du fichier audio
    console.log('Audio saved:', fileName, audioBlob);
  }}
  onCancel={() => {
    // Gérer l'annulation
    console.log('Recording cancelled');
  }}
  maxDuration={300} // 5 minutes
/>
```

## 🛠️ Configuration

### Props du composant AudioRecorder

| Prop | Type | Défaut | Description |
|------|------|--------|-------------|
| `patientId` | `string` | - | ID du patient pour le nommage des fichiers |
| `onSave` | `(blob: Blob, fileName: string) => void` | - | Callback appelé lors de la sauvegarde |
| `onCancel` | `() => void` | - | Callback appelé lors de l'annulation |
| `maxDuration` | `number` | `300` | Durée maximale en secondes |

### Paramètres d'enregistrement

```typescript
// Configuration audio dans AudioRecorder.tsx
const stream = await navigator.mediaDevices.getUserMedia({ 
  audio: {
    echoCancellation: true,    // Suppression d'écho
    noiseSuppression: true,    // Suppression de bruit
    sampleRate: 44100         // Qualité CD
  } 
});

const mediaRecorder = new MediaRecorder(stream, {
  mimeType: 'audio/webm;codecs=opus'  // Format optimisé
});
```

## 🧪 Tests et démonstration

### 1. Interface de test

Utilisez `AudioRecorderTest.tsx` pour tester toutes les fonctionnalités :

```typescript
import AudioRecorderTest from './AudioRecorderTest';

// Dans votre composant
<AudioRecorderTest />
```

### 2. Page de démonstration

Accédez à `demo-audio.tsx` pour une démonstration complète avec :
- Tests de permissions
- Enregistrement et lecture
- Sauvegarde et téléchargement
- Logs détaillés

### 3. Tests manuels recommandés

1. **Test des permissions** : Vérifier l'accès microphone
2. **Test d'enregistrement court** : 10-15 secondes
3. **Test pause/reprise** : Interrompre et reprendre
4. **Test limite de durée** : Atteindre la limite maximale
5. **Test de lecture** : Vérifier la qualité audio
6. **Test de sauvegarde** : Confirmer l'ajout aux fichiers
7. **Test de téléchargement** : Vérifier le fichier téléchargé

## 🔒 Sécurité et permissions

### Permissions requises

- **Microphone** : Accès obligatoire pour l'enregistrement
- **HTTPS** : Requis en production pour les API media
- **Stockage local** : Pour les URLs temporaires des blobs

### Gestion des erreurs

```typescript
// Exemples d'erreurs gérées
- "Impossible d'accéder au microphone"
- "Permissions refusées"
- "Format audio non supporté"
- "Erreur lors de l'enregistrement"
```

### Bonnes pratiques

1. **Toujours vérifier les permissions** avant l'enregistrement
2. **Nettoyer les URLs de blob** après utilisation
3. **Limiter la durée** pour éviter les fichiers trop volumineux
4. **Informer l'utilisateur** des exigences techniques

## 📊 Spécifications techniques

### Format audio
- **Container** : WebM
- **Codec** : Opus
- **Qualité** : 44.1 kHz, stéréo
- **Compression** : ~1 MB par minute

### Compatibilité navigateurs
- ✅ Chrome 47+
- ✅ Firefox 29+
- ✅ Safari 14.1+
- ✅ Edge 79+

### Dépendances
- React 18+
- TypeScript 4.5+
- @mantine/core 7+
- @tabler/icons-react

## 🚀 Intégration système

### Sauvegarde backend

```typescript
const handleAudioSave = async (audioBlob: Blob, fileName: string) => {
  const formData = new FormData();
  formData.append('audio', audioBlob, fileName);
  formData.append('patientId', patientId);
  
  try {
    const response = await fetch('/api/patients/audio', {
      method: 'POST',
      body: formData
    });
    
    if (response.ok) {
      console.log('Audio saved to server');
    }
  } catch (error) {
    console.error('Upload failed:', error);
  }
};
```

### Base de données

```sql
-- Table pour stocker les métadonnées audio
CREATE TABLE patient_audio_files (
  id UUID PRIMARY KEY,
  patient_id VARCHAR(255) NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  file_size INTEGER NOT NULL,
  duration_seconds INTEGER,
  created_at TIMESTAMP DEFAULT NOW(),
  file_path VARCHAR(500) NOT NULL
);
```

## 🐛 Dépannage

### Problèmes courants

1. **Microphone non détecté**
   - Vérifier les permissions du navigateur
   - Tester avec un autre navigateur
   - Vérifier les paramètres système

2. **Enregistrement silencieux**
   - Vérifier le niveau du microphone
   - Tester avec d'autres applications
   - Vérifier les paramètres audio système

3. **Fichier non sauvegardé**
   - Vérifier la console pour les erreurs
   - Tester la fonction onSave
   - Vérifier l'espace disque disponible

### Debug

Activez les logs détaillés en ajoutant dans la console :

```javascript
// Activer les logs MediaRecorder
localStorage.setItem('debug-audio', 'true');
```

## 📞 Support

Pour toute question ou problème :

1. Consultez les logs de la console navigateur
2. Testez avec l'interface AudioRecorderTest
3. Vérifiez la compatibilité navigateur
4. Consultez la documentation MDN pour MediaRecorder API

---

**Version** : 1.0  
**Dernière mise à jour** : 28/06/2025  
**Auteur** : Équipe développement
